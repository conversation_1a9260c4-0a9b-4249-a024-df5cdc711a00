# Deep Research API - Testing Guide

This guide covers testing the Deep Research API to ensure it's working correctly and ready for production use.

## Quick Test

Run the automated test script:
```bash
./test-api.sh
```

For authenticated testing:
```bash
API_KEY=your_api_key ./test-api.sh
```

## Manual Testing

### 1. Health Check
Verify the API is running:
```bash
curl http://localhost:3051/api/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "version": "1.0.0",
  "uptime": 123.45,
  "memory": {
    "used": 150,
    "total": 200,
    "percentage": 75
  },
  "tasks": {
    "total": 5,
    "pending": 1,
    "running": 2,
    "completed": 2,
    "failed": 0,
    "cancelled": 0
  }
}
```

### 2. API Documentation
Access the interactive documentation:
```bash
# Open in browser
open http://localhost:3051/docs

# Or get API info
curl http://localhost:3051/api/info
```

### 3. Start Research Task
Create a new research task:
```bash
curl -X POST http://localhost:3051/api/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "query": "Benefits of renewable energy",
    "depth": 2,
    "breadth": 3,
    "outputType": "report"
  }'
```

Expected response:
```json
{
  "taskId": "uuid-here",
  "status": "pending",
  "message": "Research task queued successfully",
  "estimatedDuration": "180s",
  "priority": "normal",
  "createdAt": "2024-01-01T12:00:00.000Z"
}
```

### 4. Check Task Status
Monitor task progress:
```bash
# Replace {taskId} with actual task ID
curl http://localhost:3051/api/research/{taskId} \
  -H "X-API-Key: your_api_key"
```

### 5. Get Task Progress
Real-time progress updates:
```bash
curl http://localhost:3051/api/research/{taskId}/progress \
  -H "X-API-Key: your_api_key"
```

### 6. List Tasks
Get all tasks:
```bash
curl "http://localhost:3051/api/research?page=1&limit=10" \
  -H "X-API-Key: your_api_key"
```

### 7. Cancel Task
Cancel a running task:
```bash
curl -X POST http://localhost:3051/api/research/{taskId}/cancel \
  -H "X-API-Key: your_api_key"
```

## Authentication Testing

### Without Authentication
If `REQUIRE_AUTH=false`:
```bash
curl http://localhost:3051/api/health
# Should work without API key
```

### With Authentication
If `REQUIRE_AUTH=true`:
```bash
# Without API key - should fail
curl http://localhost:3051/api/research

# With API key - should work
curl -H "X-API-Key: your_api_key" http://localhost:3051/api/research

# With Bearer token - should work
curl -H "Authorization: Bearer your_api_key" http://localhost:3051/api/research
```

## Rate Limiting Testing

Test rate limiting behavior:
```bash
# Send multiple requests quickly
for i in {1..10}; do
  curl -w "%{http_code}\n" -o /dev/null -s \
    http://localhost:3051/api/health
done
```

Check rate limit headers:
```bash
curl -I http://localhost:3051/api/health
# Look for X-RateLimit-* headers
```

## Error Handling Testing

### Invalid Requests
```bash
# Missing required field
curl -X POST http://localhost:3051/api/research \
  -H "Content-Type: application/json" \
  -d '{}'

# Invalid task ID format
curl http://localhost:3051/api/research/invalid-uuid

# Invalid parameters
curl -X POST http://localhost:3051/api/research \
  -H "Content-Type: application/json" \
  -d '{
    "query": "",
    "depth": 0,
    "breadth": 0
  }'
```

### Authentication Errors
```bash
# Invalid API key
curl -H "X-API-Key: invalid_key" http://localhost:3051/api/research

# Missing API key (when required)
curl http://localhost:3051/api/research
```

## Performance Testing

### Load Testing with curl
```bash
# Simple load test
for i in {1..50}; do
  curl -s http://localhost:3051/api/health > /dev/null &
done
wait
```

### Concurrent Task Testing
```bash
# Start multiple research tasks
for i in {1..5}; do
  curl -X POST http://localhost:3051/api/research \
    -H "Content-Type: application/json" \
    -H "X-API-Key: your_api_key" \
    -d "{
      \"query\": \"Test query $i\",
      \"depth\": 1,
      \"breadth\": 2
    }" &
done
wait
```

## Integration Testing

### N8N Workflow Testing
1. Import example workflows from `n8n-examples/`
2. Configure credentials and base URL
3. Test each workflow manually
4. Verify results and error handling

### Webhook Testing
```bash
# Test webhook endpoint (if using webhook workflow)
curl -X POST http://your-n8n-instance/webhook/research \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Test research topic",
    "email": "<EMAIL>"
  }'
```

## Production Readiness Checklist

### Security
- [ ] Authentication enabled (`REQUIRE_AUTH=true`)
- [ ] Strong API keys configured
- [ ] Security headers enabled
- [ ] HTTPS configured (production)
- [ ] CORS properly configured
- [ ] Rate limiting configured

### Performance
- [ ] Appropriate concurrency limits set
- [ ] Memory usage within limits
- [ ] Response times acceptable
- [ ] Error rates low

### Monitoring
- [ ] Health endpoint responding
- [ ] Logs being generated
- [ ] Resource usage monitored
- [ ] Error tracking in place

### Functionality
- [ ] All API endpoints working
- [ ] Research tasks completing successfully
- [ ] Progress tracking working
- [ ] Task cancellation working
- [ ] Error handling working

## Troubleshooting Tests

### Common Issues

#### API Not Responding
```bash
# Check if service is running
docker-compose ps

# Check logs
docker-compose logs deep-research-api

# Check port binding
netstat -tlnp | grep 3051
```

#### Authentication Issues
```bash
# Verify API key
echo "API_KEY=$API_KEY" | wc -c  # Should be 32+ chars

# Test with curl verbose
curl -v -H "X-API-Key: $API_KEY" http://localhost:3051/api/health
```

#### Research Tasks Failing
```bash
# Check API keys are valid
curl -H "Authorization: Bearer $FIRECRAWL_KEY" \
  https://api.firecrawl.dev/v0/search

curl -H "Authorization: Bearer $OPENAI_KEY" \
  https://api.openai.com/v1/models
```

#### Memory Issues
```bash
# Check memory usage
docker stats

# Check system memory
free -h

# Restart if needed
docker-compose restart
```

## Automated Testing

### Continuous Testing Script
Create a monitoring script:
```bash
#!/bin/bash
# monitor.sh
while true; do
  if ! curl -f http://localhost:3051/api/health > /dev/null 2>&1; then
    echo "$(date): API health check failed"
    # Add notification logic here
  fi
  sleep 60
done
```

### Test Data Validation
Verify research results:
```bash
# Check if results contain expected fields
curl http://localhost:3051/api/research/{taskId} | \
  jq '.result | has("report") and has("learnings") and has("visitedUrls")'
```

## Performance Benchmarks

### Expected Performance
- **Health Check**: < 100ms
- **Task Creation**: < 500ms
- **Status Check**: < 200ms
- **Research Completion**: 30s - 10min (depending on depth/breadth)

### Memory Usage
- **Idle**: ~100MB
- **Active Research**: ~200-500MB per concurrent task
- **Maximum**: Should not exceed available system memory

### Concurrent Limits
- **Default**: 10 concurrent tasks
- **Recommended**: Adjust based on available resources
- **Maximum**: Limited by memory and API rate limits
