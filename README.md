# Deep Research API

A production-ready REST API service for AI-powered research that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.

**🚀 Now with REST API for external integrations!**

Perfect for integration with workflow automation tools like N8N, Zapier, or custom applications that need intelligent research capabilities.

If you like this project, please consider starring it and giving me a follow on [X/Twitter](https://x.com/dzhng). This project is sponsored by [Aomni](https://aomni.com).

## How It Works

```mermaid
flowchart TB
    subgraph Input
        Q[User Query]
        B[Breadth Parameter]
        D[Depth Parameter]
    end

    DR[Deep Research] -->
    SQ[SERP Queries] -->
    PR[Process Results]

    subgraph Results[Results]
        direction TB
        NL((Learnings))
        ND((Directions))
    end

    PR --> NL
    PR --> ND

    DP{depth > 0?}

    RD["Next Direction:
    - Prior Goals
    - New Questions
    - Learnings"]

    MR[Markdown Report]

    %% Main Flow
    Q & B & D --> DR

    %% Results to Decision
    NL & ND --> DP

    %% Circular Flow
    DP -->|Yes| RD
    RD -->|New Context| DR

    %% Final Output
    DP -->|No| MR

    %% Styling
    classDef input fill:#7bed9f,stroke:#2ed573,color:black
    classDef process fill:#70a1ff,stroke:#1e90ff,color:black
    classDef recursive fill:#ffa502,stroke:#ff7f50,color:black
    classDef output fill:#ff4757,stroke:#ff6b81,color:black
    classDef results fill:#a8e6cf,stroke:#3b7a57,color:black

    class Q,B,D input
    class DR,SQ,PR process
    class DP,RD recursive
    class MR output
    class NL,ND results
```

## Features

### Core Research Capabilities
- **Iterative Research**: Performs deep research by iteratively generating search queries, processing results, and diving deeper based on findings
- **Intelligent Query Generation**: Uses LLMs to generate targeted search queries based on research goals and previous findings
- **Depth & Breadth Control**: Configurable parameters to control how wide (breadth) and deep (depth) the research goes
- **Smart Follow-up**: Generates follow-up questions to better understand research needs
- **Comprehensive Reports**: Produces detailed markdown reports with findings and sources
- **Concurrent Processing**: Handles multiple searches and result processing in parallel for efficiency

### REST API Features
- **Asynchronous Processing**: Start research tasks and check progress without blocking
- **Real-time Progress**: Track research progress with detailed status updates
- **Multiple Output Formats**: Get concise answers or detailed reports
- **Task Management**: List, monitor, and cancel research tasks
- **Rate Limiting**: Built-in protection against abuse with configurable limits
- **Authentication**: Optional API key authentication for production use
- **Interactive Documentation**: Swagger UI for easy API exploration and testing
- **External Access**: Configured for integration with external tools and services

### Production Ready
- **Docker Support**: Easy deployment with Docker and Docker Compose
- **Security Headers**: Production-ready security configuration
- **Health Monitoring**: Built-in health checks and monitoring endpoints
- **Error Handling**: Comprehensive error handling with detailed error messages
- **Logging**: Structured logging for debugging and monitoring
- **Scalability**: Designed for concurrent requests and high availability

## Requirements

- Node.js environment
- API keys for:
  - Firecrawl API (for web search and content extraction)
  - OpenAI API (for o3 mini model)

## Quick Start

### 1. Clone and Configure
```bash
git clone <repository-url>
cd deep-research
cp .env.example .env.local
# Edit .env.local with your API keys (see Configuration section)
```

### 2. Deploy the API
```bash
# Make deploy script executable
chmod +x deploy.sh

# Deploy for development
./deploy.sh dev

# Or deploy for production with nginx
./deploy.sh prod
```

### 3. Access the API
- **API Base URL**: `http://localhost:3051`
- **Interactive Documentation**: `http://localhost:3051/docs`
- **Health Check**: `http://localhost:3051/api/health`
- **API Info**: `http://localhost:3051/api/info`

## Configuration

### Required API Keys
Set these in your `.env.local` file:

```bash
# Required: Web search and content extraction
FIRECRAWL_KEY="your_firecrawl_key"

# Required: AI model access (choose one)
OPENAI_KEY="your_openai_key"
# OR
FIREWORKS_KEY="your_fireworks_key"  # For DeepSeek R1 model
```

### Optional Configuration
```bash
# API Settings
PORT=3051
REQUIRE_AUTH=false  # Set to true for production
API_KEY="your_secure_api_key"  # Required if REQUIRE_AUTH=true

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
MAX_CONCURRENT_TASKS=10

# CORS (adjust for your domain)
CORS_ORIGIN=*

# Security (recommended for production)
ENABLE_SECURITY_HEADERS=true
TRUST_PROXY=true
```

### Alternative AI Models
```bash
# For local LLM servers
OPENAI_ENDPOINT="http://localhost:1234/v1"
CUSTOM_MODEL="your_model_name"

# For self-hosted Firecrawl
FIRECRAWL_BASE_URL="http://localhost:3002"
```

## Usage

### REST API Usage

#### 1. Start a Research Task
```bash
curl -X POST http://localhost:3051/api/research \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Latest developments in quantum computing",
    "depth": 3,
    "breadth": 4,
    "outputType": "report"
  }'
```

Response:
```json
{
  "taskId": "uuid-here",
  "status": "pending",
  "message": "Research task queued successfully",
  "estimatedDuration": "360s"
}
```

#### 2. Check Task Status
```bash
curl http://localhost:3051/api/research/{taskId}
```

#### 3. Get Results
When status is "completed", the response includes the full research report:
```json
{
  "taskId": "uuid-here",
  "status": "completed",
  "result": {
    "report": "# Research Report\n\n...",
    "learnings": ["Key finding 1", "Key finding 2"],
    "visitedUrls": ["https://example.com", "..."]
  },
  "duration": 245000
}
```

### Command Line Usage (Original)
For direct command-line usage:

```bash
npm start
```

You'll be prompted to enter your research query and parameters. The system will generate a comprehensive report saved as `report.md`.

## API Endpoints

### Core Endpoints
- `POST /api/research` - Start a new research task
- `GET /api/research/{taskId}` - Get task status and results
- `GET /api/research/{taskId}/progress` - Get real-time progress
- `POST /api/research/{taskId}/cancel` - Cancel a running task
- `GET /api/research` - List all tasks (with pagination)
- `GET /api/health` - Health check

### Documentation
- `GET /docs` - Interactive Swagger UI documentation
- `GET /openapi.json` - OpenAPI specification
- `GET /api/info` - API information and configuration

### Authentication
When `REQUIRE_AUTH=true`, include your API key:
```bash
# Using X-API-Key header
curl -H "X-API-Key: your_api_key" http://localhost:3051/api/research

# Using Authorization header
curl -H "Authorization: Bearer your_api_key" http://localhost:3051/api/research
```

## N8N Integration

This API is designed for easy integration with N8N and other workflow automation tools.

### Quick N8N Setup
1. Import workflows from the `n8n-examples/` directory
2. Configure HTTP credentials with your API key (if authentication is enabled)
3. Update the base URL to point to your API instance
4. Test the workflows

### Available N8N Workflows
- **Basic Research**: Simple research task with result retrieval
- **Scheduled Research**: Daily/weekly automated research reports
- **Webhook Research**: Trigger research via webhooks with notifications
- **Multi-topic Research**: Research multiple topics in parallel

See the `n8n-examples/` directory for complete workflow files and documentation.

### Example N8N HTTP Request Node
```json
{
  "method": "POST",
  "url": "http://your-server:3051/api/research",
  "headers": {
    "Content-Type": "application/json",
    "X-API-Key": "your_api_key"
  },
  "body": {
    "query": "{{ $json.research_topic }}",
    "depth": 3,
    "breadth": 4,
    "outputType": "report"
  }
}
```

### Performance Tuning

#### Concurrency Settings
```bash
# Increase for faster processing (if you have higher API limits)
FIRECRAWL_CONCURRENCY=5
MAX_CONCURRENT_TASKS=20

# Reduce for free tier usage
FIRECRAWL_CONCURRENCY=1
MAX_CONCURRENT_TASKS=3
```

### DeepSeek R1

Deep research performs great on R1! We use [Fireworks](http://fireworks.ai) as the main provider for the R1 model. To use R1, simply set a Fireworks API key:

```bash
FIREWORKS_KEY="api_key"
```

The system will automatically switch over to use R1 instead of `o3-mini` when the key is detected.

### Custom endpoints and models

There are 2 other optional env vars that lets you tweak the endpoint (for other OpenAI compatible APIs like OpenRouter or Gemini) as well as the model string.

```bash
OPENAI_ENDPOINT="custom_endpoint"
CUSTOM_MODEL="custom_model"
```

## Production Deployment

### Docker Deployment
```bash
# Development deployment
./deploy.sh dev

# Production deployment with nginx
./deploy.sh prod

# Check status
./deploy.sh status

# Stop services
./deploy.sh stop
```

### Manual Docker
```bash
# Build and run
docker build -t deep-research-api .
docker run -d -p 3051:3051 --env-file .env.local deep-research-api

# With docker-compose
docker-compose up -d
```

### External Access Configuration
1. **Configure Firewall**: Open ports 80, 443, 3051
2. **Set up DNS**: Point your domain to the server IP
3. **SSL/HTTPS**: Use Let's Encrypt or your own certificates
4. **Security**: Enable authentication and security headers

See `DEPLOYMENT.md` for detailed production deployment instructions.

### Monitoring
```bash
# Health check
curl http://localhost:3051/api/health

# Service status
docker-compose ps

# View logs
docker-compose logs -f deep-research-api
```

## Security

### Production Security Checklist
- [ ] Enable authentication (`REQUIRE_AUTH=true`)
- [ ] Use strong API keys (32+ characters)
- [ ] Configure proper CORS origins
- [ ] Enable security headers
- [ ] Use HTTPS in production
- [ ] Set up rate limiting
- [ ] Regular security updates
- [ ] Monitor access logs

### API Key Management
```bash
# Generate secure API key
openssl rand -hex 32

# Set in .env.local
API_KEY=your_generated_key_here
REQUIRE_AUTH=true
```

## How It Works

1. **Initial Setup**

   - Takes user query and research parameters (breadth & depth)
   - Generates follow-up questions to understand research needs better

2. **Deep Research Process**

   - Generates multiple SERP queries based on research goals
   - Processes search results to extract key learnings
   - Generates follow-up research directions

3. **Recursive Exploration**

   - If depth > 0, takes new research directions and continues exploration
   - Each iteration builds on previous learnings
   - Maintains context of research goals and findings

4. **Report Generation**
   - Compiles all findings into a comprehensive markdown report
   - Includes all sources and references
   - Organizes information in a clear, readable format

## Troubleshooting

### Common Issues

#### API Connection Issues
```bash
# Check if service is running
curl http://localhost:3051/api/health

# Check Docker containers
docker-compose ps

# View logs
docker-compose logs deep-research-api
```

#### Authentication Problems
```bash
# Verify API key format
echo $API_KEY | wc -c  # Should be 32+ characters

# Test authentication
curl -H "X-API-Key: $API_KEY" http://localhost:3051/api/health
```

#### Rate Limiting
```bash
# Check rate limit headers in response
curl -I http://localhost:3051/api/research

# Adjust limits in .env.local
RATE_LIMIT_MAX_REQUESTS=200
MAX_CONCURRENT_TASKS=15
```

#### Memory Issues
```bash
# Check memory usage
docker stats

# Restart services
docker-compose restart
```

### Debug Mode
Enable detailed logging:
```bash
# Set in .env.local
LOG_LEVEL=debug
NODE_ENV=development

# Restart and check logs
docker-compose restart
docker-compose logs -f deep-research-api
```

## Examples and Use Cases

### Research Automation
- **Daily Market Reports**: Automated research on market trends
- **Competitive Analysis**: Regular competitor monitoring
- **Technology Scouting**: Track emerging technologies
- **News Monitoring**: Automated news analysis and summaries

### Integration Examples
- **N8N Workflows**: Automated research pipelines
- **Zapier Integration**: Connect with 5000+ apps
- **Custom Applications**: Embed research capabilities
- **Slack Bots**: Research on-demand in team channels

### API Response Examples
See the interactive documentation at `/docs` for complete API examples and schemas.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Setup
```bash
git clone <your-fork>
cd deep-research
npm install
cp .env.example .env.local
# Edit .env.local with your API keys
npm run api  # Start development server
```

## Support

- **Documentation**: Visit `/docs` on your running instance
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions
- **API Reference**: Complete OpenAPI specification at `/openapi.json`

## License

MIT License - feel free to use and modify as needed.

## Acknowledgments

- Original concept and implementation
- [Aomni](https://aomni.com) for sponsorship
- Community contributors and feedback
