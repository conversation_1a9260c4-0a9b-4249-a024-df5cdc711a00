# Caddyfile for Deep Research API
# Automatic HTTPS with Let's Encrypt for deep.w-post.com

# Global options (must be first)
{
    # Email for Let's Encrypt
    email <EMAIL>

    # Use Let's Encrypt production environment
    acme_ca https://acme-v02.api.letsencrypt.org/directory

    # Global logging
    log {
        level INFO
    }

    # Admin API (for monitoring)
    admin localhost:2019
}

# Main domain configuration
deep.w-post.com {
    # Reverse proxy to the API service
    reverse_proxy deep-research-api:3051 {
        # Headers for proper forwarding
        header_up Host {host}
        header_up X-Real-IP {remote_host}
    }

    # Security headers
    header {
        # HSTS
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

        # Content security
        X-Content-Type-Options "nosniff"
        X-Frame-Options "DENY"
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"

        # Remove server info
        -Server

        # CORS headers (will be handled by the API, but backup)
        Access-Control-Allow-Origin "*"
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization, X-API-Key"
    }

    # Logging
    log {
        output file /var/log/caddy/access.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
        level INFO
    }

    # Error handling
    handle_errors {
        @5xx expression {http.error.status_code} >= 500
        respond @5xx "Service temporarily unavailable" 503

        @4xx expression {http.error.status_code} >= 400
        respond @4xx "Bad request" 400
    }

    # Compression
    encode {
        gzip 6
        minimum_length 1024
        match {
            header Content-Type application/json*
            header Content-Type application/javascript*
            header Content-Type text/*
        }
    }
}

