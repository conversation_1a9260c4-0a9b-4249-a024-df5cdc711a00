export const swaggerSpec = {
  openapi: '3.0.3',
  info: {
    title: 'Deep Research API',
    version: '1.0.0',
    description: `
# Deep Research API

An AI-powered research assistant API that performs iterative, deep research on any topic by combining search engines, web scraping, and large language models.

## Features

- **Asynchronous Research**: Start research tasks and check their progress
- **Configurable Depth & Breadth**: Control how wide and deep the research goes
- **Multiple Output Formats**: Get concise answers or detailed reports
- **Real-time Progress**: Track research progress in real-time
- **Rate Limiting**: Built-in protection against abuse
- **Authentication**: Optional API key authentication for production use

## Authentication

When authentication is enabled (REQUIRE_AUTH=true), include your API key in requests:

- **Header**: \`X-API-Key: your_api_key\`
- **Bearer Token**: \`Authorization: Bearer your_api_key\`

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- Default: 100 requests per 15 minutes per client
- Rate limit headers are included in responses
- Concurrent task limits apply (default: 10 concurrent tasks)

## Error Handling

All errors follow a consistent format with appropriate HTTP status codes and detailed error messages.
    `,
    contact: {
      name: 'Deep Research API Support',
      url: 'https://github.com/your-repo/deep-research',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: 'http://localhost:3051',
      description: 'Development server',
    },
    {
      url: 'https://your-domain.com',
      description: 'Production server',
    },
  ],
  security: [
    {
      ApiKeyAuth: [],
    },
    {
      BearerAuth: [],
    },
  ],
  components: {
    securitySchemes: {
      ApiKeyAuth: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-Key',
        description: 'API key for authentication',
      },
      BearerAuth: {
        type: 'http',
        scheme: 'bearer',
        description: 'Bearer token authentication',
      },
    },
    schemas: {
      ResearchRequest: {
        type: 'object',
        required: ['query'],
        properties: {
          query: {
            type: 'string',
            minLength: 1,
            maxLength: 1000,
            description: 'The research query or topic to investigate',
            example: 'What are the latest developments in quantum computing?',
          },
          depth: {
            type: 'integer',
            minimum: 1,
            maximum: 10,
            default: 3,
            description: 'How deep to research (number of iterations)',
          },
          breadth: {
            type: 'integer',
            minimum: 1,
            maximum: 20,
            default: 3,
            description: 'How many search queries to generate per iteration',
          },
          outputType: {
            type: 'string',
            enum: ['answer', 'report'],
            default: 'report',
            description: 'Type of output to generate',
          },
          priority: {
            type: 'string',
            enum: ['low', 'normal', 'high'],
            default: 'normal',
            description: 'Task priority level',
          },
          timeout: {
            type: 'integer',
            minimum: 60,
            maximum: 3600,
            description: 'Task timeout in seconds (optional)',
          },
          metadata: {
            type: 'object',
            additionalProperties: true,
            description: 'Optional metadata for the research task',
          },
        },
      },
      ResearchProgress: {
        type: 'object',
        properties: {
          currentDepth: {
            type: 'integer',
            description: 'Current research depth level',
          },
          totalDepth: {
            type: 'integer',
            description: 'Total depth levels to complete',
          },
          currentBreadth: {
            type: 'integer',
            description: 'Current breadth level',
          },
          totalBreadth: {
            type: 'integer',
            description: 'Total breadth levels',
          },
          currentQuery: {
            type: 'string',
            description: 'Currently executing search query',
          },
          totalQueries: {
            type: 'integer',
            description: 'Total number of queries to execute',
          },
          completedQueries: {
            type: 'integer',
            description: 'Number of completed queries',
          },
        },
      },
      ResearchResult: {
        type: 'object',
        properties: {
          answer: {
            type: 'string',
            description: 'Concise answer (when outputType is "answer")',
          },
          report: {
            type: 'string',
            description: 'Detailed report (when outputType is "report")',
          },
          learnings: {
            type: 'array',
            items: {
              type: 'string',
            },
            description: 'Key learnings extracted during research',
          },
          visitedUrls: {
            type: 'array',
            items: {
              type: 'string',
              format: 'uri',
            },
            description: 'URLs visited during research',
          },
        },
      },
      TaskResponse: {
        type: 'object',
        properties: {
          taskId: {
            type: 'string',
            format: 'uuid',
            description: 'Unique task identifier',
          },
          status: {
            type: 'string',
            enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
            description: 'Current task status',
          },
          message: {
            type: 'string',
            description: 'Status message',
          },
          estimatedDuration: {
            type: 'string',
            description: 'Estimated completion time',
          },
          priority: {
            type: 'string',
            enum: ['low', 'normal', 'high'],
            description: 'Task priority',
          },
          timeout: {
            type: 'integer',
            description: 'Task timeout in seconds',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Task creation timestamp',
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Last update timestamp',
          },
          completedAt: {
            type: 'string',
            format: 'date-time',
            description: 'Task completion timestamp',
          },
          duration: {
            type: 'integer',
            description: 'Task duration in milliseconds',
          },
          request: {
            $ref: '#/components/schemas/ResearchRequest',
          },
          progress: {
            $ref: '#/components/schemas/ResearchProgress',
          },
          result: {
            $ref: '#/components/schemas/ResearchResult',
          },
          error: {
            type: 'string',
            description: 'Error message if task failed',
          },
          queuePosition: {
            type: 'integer',
            description: 'Position in the execution queue',
          },
        },
      },
      ApiError: {
        type: 'object',
        properties: {
          error: {
            type: 'string',
            description: 'Error type',
          },
          message: {
            type: 'string',
            description: 'Human-readable error message',
          },
          code: {
            type: 'string',
            description: 'Error code for programmatic handling',
          },
          details: {
            type: 'object',
            description: 'Additional error details',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Error timestamp',
          },
        },
      },
      HealthResponse: {
        type: 'object',
        properties: {
          status: {
            type: 'string',
            enum: ['healthy', 'unhealthy'],
            description: 'Service health status',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Health check timestamp',
          },
          version: {
            type: 'string',
            description: 'API version',
          },
          uptime: {
            type: 'number',
            description: 'Server uptime in seconds',
          },
          memory: {
            type: 'object',
            properties: {
              used: {
                type: 'integer',
                description: 'Used memory in MB',
              },
              total: {
                type: 'integer',
                description: 'Total memory in MB',
              },
              percentage: {
                type: 'integer',
                description: 'Memory usage percentage',
              },
            },
          },
          tasks: {
            type: 'object',
            properties: {
              total: {
                type: 'integer',
                description: 'Total number of tasks',
              },
              pending: {
                type: 'integer',
                description: 'Number of pending tasks',
              },
              running: {
                type: 'integer',
                description: 'Number of running tasks',
              },
              completed: {
                type: 'integer',
                description: 'Number of completed tasks',
              },
              failed: {
                type: 'integer',
                description: 'Number of failed tasks',
              },
              cancelled: {
                type: 'integer',
                description: 'Number of cancelled tasks',
              },
            },
          },
        },
      },
    },
  },
  paths: {
    '/api/health': {
      get: {
        tags: ['Health'],
        summary: 'Health check',
        description: 'Check the health status of the API service',
        security: [],
        responses: {
          '200': {
            description: 'Service is healthy',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/HealthResponse',
                },
              },
            },
          },
        },
      },
    },
    '/api/research': {
      post: {
        tags: ['Research'],
        summary: 'Start a new research task',
        description: 'Initiate a new asynchronous research task',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/ResearchRequest',
              },
              examples: {
                'simple-query': {
                  summary: 'Simple research query',
                  value: {
                    query: 'What are the latest developments in quantum computing?',
                    depth: 3,
                    breadth: 4,
                    outputType: 'report',
                  },
                },
                'quick-answer': {
                  summary: 'Quick answer request',
                  value: {
                    query: 'What is the current price of Bitcoin?',
                    depth: 1,
                    breadth: 2,
                    outputType: 'answer',
                  },
                },
                'deep-research': {
                  summary: 'Deep research with high priority',
                  value: {
                    query: 'Comprehensive analysis of renewable energy trends in 2024',
                    depth: 5,
                    breadth: 6,
                    outputType: 'report',
                    priority: 'high',
                    timeout: 1800,
                  },
                },
              },
            },
          },
        },
        responses: {
          '202': {
            description: 'Research task started successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    taskId: {
                      type: 'string',
                      format: 'uuid',
                    },
                    status: {
                      type: 'string',
                      enum: ['pending'],
                    },
                    message: {
                      type: 'string',
                    },
                    estimatedDuration: {
                      type: 'string',
                    },
                    priority: {
                      type: 'string',
                    },
                    createdAt: {
                      type: 'string',
                      format: 'date-time',
                    },
                    queuePosition: {
                      type: 'integer',
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Invalid request data',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
          '401': {
            description: 'Authentication required',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
          '429': {
            description: 'Rate limit exceeded or max concurrent tasks reached',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
        },
      },
      get: {
        tags: ['Research'],
        summary: 'List research tasks',
        description: 'Get a paginated list of research tasks',
        parameters: [
          {
            name: 'page',
            in: 'query',
            description: 'Page number',
            schema: {
              type: 'integer',
              minimum: 1,
              default: 1,
            },
          },
          {
            name: 'limit',
            in: 'query',
            description: 'Number of tasks per page',
            schema: {
              type: 'integer',
              minimum: 1,
              maximum: 100,
              default: 10,
            },
          },
          {
            name: 'status',
            in: 'query',
            description: 'Filter by task status',
            schema: {
              type: 'string',
              enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
            },
          },
        ],
        responses: {
          '200': {
            description: 'List of research tasks',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    tasks: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          taskId: {
                            type: 'string',
                            format: 'uuid',
                          },
                          status: {
                            type: 'string',
                            enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
                          },
                          query: {
                            type: 'string',
                          },
                          outputType: {
                            type: 'string',
                            enum: ['answer', 'report'],
                          },
                          priority: {
                            type: 'string',
                            enum: ['low', 'normal', 'high'],
                          },
                          createdAt: {
                            type: 'string',
                            format: 'date-time',
                          },
                          updatedAt: {
                            type: 'string',
                            format: 'date-time',
                          },
                          completedAt: {
                            type: 'string',
                            format: 'date-time',
                          },
                          progress: {
                            $ref: '#/components/schemas/ResearchProgress',
                          },
                        },
                      },
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        page: {
                          type: 'integer',
                        },
                        limit: {
                          type: 'integer',
                        },
                        total: {
                          type: 'integer',
                        },
                        totalPages: {
                          type: 'integer',
                        },
                        hasNext: {
                          type: 'boolean',
                        },
                        hasPrev: {
                          type: 'boolean',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Invalid query parameters',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
        },
      },
    },
    '/api/research/{taskId}': {
      get: {
        tags: ['Research'],
        summary: 'Get research task details',
        description: 'Get detailed information about a specific research task',
        parameters: [
          {
            name: 'taskId',
            in: 'path',
            required: true,
            description: 'Task ID',
            schema: {
              type: 'string',
              format: 'uuid',
            },
          },
        ],
        responses: {
          '200': {
            description: 'Task details',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/TaskResponse',
                },
              },
            },
          },
          '400': {
            description: 'Invalid task ID format',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
          '404': {
            description: 'Task not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
        },
      },
    },
    '/api/research/{taskId}/progress': {
      get: {
        tags: ['Research'],
        summary: 'Get task progress',
        description: 'Get real-time progress information for a research task',
        parameters: [
          {
            name: 'taskId',
            in: 'path',
            required: true,
            description: 'Task ID',
            schema: {
              type: 'string',
              format: 'uuid',
            },
          },
        ],
        responses: {
          '200': {
            description: 'Task progress',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    taskId: {
                      type: 'string',
                      format: 'uuid',
                    },
                    status: {
                      type: 'string',
                      enum: ['pending', 'running', 'completed', 'failed', 'cancelled'],
                    },
                    progress: {
                      $ref: '#/components/schemas/ResearchProgress',
                    },
                    updatedAt: {
                      type: 'string',
                      format: 'date-time',
                    },
                  },
                },
              },
            },
          },
          '404': {
            description: 'Task not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
        },
      },
    },
    '/api/research/{taskId}/cancel': {
      post: {
        tags: ['Research'],
        summary: 'Cancel research task',
        description: 'Cancel a pending or running research task',
        parameters: [
          {
            name: 'taskId',
            in: 'path',
            required: true,
            description: 'Task ID',
            schema: {
              type: 'string',
              format: 'uuid',
            },
          },
        ],
        responses: {
          '200': {
            description: 'Task cancelled successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    taskId: {
                      type: 'string',
                      format: 'uuid',
                    },
                    status: {
                      type: 'string',
                      enum: ['cancelled'],
                    },
                    message: {
                      type: 'string',
                    },
                    updatedAt: {
                      type: 'string',
                      format: 'date-time',
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Cannot cancel task (already completed or failed)',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
          '404': {
            description: 'Task not found',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
        },
      },
    },
    '/api/generate-report': {
      post: {
        tags: ['Legacy'],
        summary: 'Generate report (legacy)',
        description: 'Legacy endpoint for synchronous report generation. Use /api/research for new implementations.',
        deprecated: true,
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['query'],
                properties: {
                  query: {
                    type: 'string',
                    description: 'Research query',
                  },
                  depth: {
                    type: 'integer',
                    default: 3,
                  },
                  breadth: {
                    type: 'integer',
                    default: 3,
                  },
                },
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Research completed',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    success: {
                      type: 'boolean',
                    },
                    report: {
                      type: 'string',
                    },
                    learnings: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                    },
                    visitedUrls: {
                      type: 'array',
                      items: {
                        type: 'string',
                      },
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Invalid request',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
          '500': {
            description: 'Research failed',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ApiError',
                },
              },
            },
          },
        },
      },
    },
    '/api/docs': {
      get: {
        tags: ['Documentation'],
        summary: 'API documentation',
        description: 'Get API documentation and endpoint information',
        security: [],
        responses: {
          '200': {
            description: 'API documentation',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    title: {
                      type: 'string',
                    },
                    version: {
                      type: 'string',
                    },
                    description: {
                      type: 'string',
                    },
                    endpoints: {
                      type: 'object',
                    },
                    schemas: {
                      type: 'object',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  tags: [
    {
      name: 'Health',
      description: 'Health check and monitoring endpoints',
    },
    {
      name: 'Research',
      description: 'Research task management endpoints',
    },
    {
      name: 'Legacy',
      description: 'Legacy endpoints for backward compatibility',
    },
    {
      name: 'Documentation',
      description: 'API documentation endpoints',
    },
  ],
};
