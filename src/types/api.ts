import { z } from 'zod';
import { ResearchProgress } from '../deep-research';

// Request Schemas
export const ResearchRequestSchema = z.object({
  query: z.string()
    .min(1, 'Query is required')
    .max(1000, 'Query must be less than 1000 characters')
    .trim(),
  depth: z.number()
    .int('Depth must be an integer')
    .min(1, 'Depth must be at least 1')
    .max(10, 'Depth cannot exceed 10')
    .default(3),
  breadth: z.number()
    .int('Breadth must be an integer')
    .min(1, 'Breadth must be at least 1')
    .max(20, 'Breadth cannot exceed 20')
    .default(3),
  outputType: z.enum(['answer', 'report'], {
    errorMap: () => ({ message: 'Output type must be either "answer" or "report"' })
  }).default('report'),
  metadata: z.record(z.any()).optional(),
  priority: z.enum(['low', 'normal', 'high']).default('normal'),
  timeout: z.number()
    .int('Timeout must be an integer')
    .min(60, 'Timeout must be at least 60 seconds')
    .max(3600, 'Timeout cannot exceed 3600 seconds')
    .optional(),
});

export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  status: z.enum(['pending', 'running', 'completed', 'failed', 'cancelled']).optional(),
});

export const TaskIdSchema = z.object({
  taskId: z.string().uuid('Invalid task ID format'),
});

// Response Types
export interface ResearchTask {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  request: ResearchRequest;
  progress?: ResearchProgress;
  result?: {
    answer?: string;
    report?: string;
    learnings: string[];
    visitedUrls: string[];
  };
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  priority: 'low' | 'normal' | 'high';
  timeout?: number;
}

export interface ApiError {
  error: string;
  message: string;
  code?: string;
  details?: any;
  timestamp?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  timestamp: string;
}

export interface TaskResponse {
  taskId: string;
  status: ResearchTask['status'];
  message?: string;
  estimatedDuration?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  duration?: number;
  request: ResearchRequest;
  progress?: ResearchProgress;
  result?: ResearchTask['result'];
  error?: string;
}

export interface TaskListResponse {
  tasks: Array<{
    taskId: string;
    status: ResearchTask['status'];
    query: string;
    outputType: 'answer' | 'report';
    priority: 'low' | 'normal' | 'high';
    createdAt: Date;
    updatedAt: Date;
    completedAt?: Date;
    progress?: ResearchProgress;
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ProgressResponse {
  taskId: string;
  status: ResearchTask['status'];
  progress: ResearchProgress | null;
  updatedAt: Date;
}

export interface HealthResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  tasks: {
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
  };
}

// Type exports
export type ResearchRequest = z.infer<typeof ResearchRequestSchema>;
export type PaginationRequest = z.infer<typeof PaginationSchema>;
export type TaskIdRequest = z.infer<typeof TaskIdSchema>;

// Validation helper types
export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
}

// Error codes
export const ErrorCodes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  TASK_NOT_FOUND: 'TASK_NOT_FOUND',
  INVALID_OPERATION: 'INVALID_OPERATION',
  RESEARCH_ERROR: 'RESEARCH_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes];
