import cors from 'cors';
import express, { Request, Response, NextFunction } from 'express';
import swaggerUi from 'swagger-ui-express';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';

import { deepResearch, writeFinalAnswer, writeFinalReport } from './deep-research';
import { swaggerSpec } from './swagger';
import {
  ResearchRequestSchema,
  PaginationSchema,
  TaskIdSchema,
  ResearchTask,
  ApiError,
  ErrorCodes,
  ValidationResult,
  TaskResponse,
  TaskListResponse,
  ProgressResponse,
  HealthResponse,
} from './types/api';

// Extended Request interface for authentication
interface AuthenticatedRequest extends Request {
  apiKey?: string;
  rateLimitInfo?: {
    remaining: number;
    resetTime: Date;
  };
}

// In-memory stores (can be replaced with Redis/Database for production)
const tasks = new Map<string, ResearchTask>();
const rateLimitStore = new Map<string, { count: number; resetTime: Date }>();

// Task cleanup configuration
const MAX_CONCURRENT_TASKS = parseInt(process.env.MAX_CONCURRENT_TASKS || '10');
const TASK_CLEANUP_INTERVAL = parseInt(process.env.TASK_CLEANUP_INTERVAL || '3600000'); // 1 hour
const TASK_RETENTION_DAYS = 7; // Keep completed tasks for 7 days

const app = express();
const port = parseInt(process.env.PORT || '3051');

// Configuration
const API_KEY = process.env.API_KEY;
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'); // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100');
const REQUIRE_AUTH = process.env.REQUIRE_AUTH === 'true';
const ENABLE_SECURITY_HEADERS = process.env.ENABLE_SECURITY_HEADERS === 'true';
const TRUST_PROXY = process.env.TRUST_PROXY === 'true';

// Trust proxy if configured (for proper IP detection behind load balancers)
if (TRUST_PROXY) {
  app.set('trust proxy', 1);
}

// Security headers middleware
if (ENABLE_SECURITY_HEADERS) {
  app.use((req: Request, res: Response, next: NextFunction) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'");
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    next();
  });
}

// CORS middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
}));

// Body parsing middleware
app.use(express.json({
  limit: '10mb',
  type: 'application/json',
}));

app.use(express.urlencoded({
  extended: true,
  limit: '10mb',
}));

// Request logging middleware
app.use((req: Request, res: Response, next: NextFunction) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Task cleanup function
function cleanupOldTasks() {
  const now = new Date();
  const retentionTime = TASK_RETENTION_DAYS * 24 * 60 * 60 * 1000;

  let cleanedCount = 0;
  for (const [taskId, task] of tasks.entries()) {
    const taskAge = now.getTime() - task.createdAt.getTime();

    // Remove old completed/failed/cancelled tasks
    if (
      (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') &&
      taskAge > retentionTime
    ) {
      tasks.delete(taskId);
      cleanedCount++;
    }
  }

  if (cleanedCount > 0) {
    console.log(`🧹 Cleaned up ${cleanedCount} old tasks`);
  }
}

// Authentication middleware
const authenticateApiKey = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!REQUIRE_AUTH) {
    return next();
  }

  const apiKey = req.headers['x-api-key'] as string ||
    req.headers['authorization']?.replace(/^Bearer\s+/i, '');

  if (!apiKey) {
    return res.status(401).json(createApiError(
      'API key is required. Provide it via X-API-Key header or Authorization: Bearer <key>',
      ErrorCodes.AUTHENTICATION_ERROR
    ));
  }

  if (API_KEY && apiKey !== API_KEY) {
    console.warn(`❌ Invalid API key attempt from ${req.ip}: ${apiKey.substring(0, 8)}...`);
    return res.status(401).json(createApiError(
      'Invalid API key',
      ErrorCodes.AUTHENTICATION_ERROR
    ));
  }

  req.apiKey = apiKey;
  next();
};

// Rate limiting middleware
const rateLimitMiddleware = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const clientId = req.apiKey || req.ip || 'unknown';
  const now = new Date();

  let clientData = rateLimitStore.get(clientId);

  if (!clientData || now > clientData.resetTime) {
    clientData = {
      count: 0,
      resetTime: new Date(now.getTime() + RATE_LIMIT_WINDOW),
    };
    rateLimitStore.set(clientId, clientData);
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return res.status(429).json(createApiError(
      'Rate limit exceeded. Please try again later.',
      ErrorCodes.RATE_LIMIT_ERROR,
      {
        resetTime: clientData.resetTime,
        maxRequests: RATE_LIMIT_MAX_REQUESTS,
        windowMs: RATE_LIMIT_WINDOW,
        retryAfter: Math.ceil((clientData.resetTime.getTime() - now.getTime()) / 1000),
      }
    ));
  }

  clientData.count++;
  req.rateLimitInfo = {
    remaining: RATE_LIMIT_MAX_REQUESTS - clientData.count,
    resetTime: clientData.resetTime,
  };

  // Add rate limit headers
  res.set({
    'X-RateLimit-Limit': RATE_LIMIT_MAX_REQUESTS.toString(),
    'X-RateLimit-Remaining': req.rateLimitInfo.remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(clientData.resetTime.getTime() / 1000).toString(),
    'X-RateLimit-Window': (RATE_LIMIT_WINDOW / 1000).toString(),
  });

  next();
};

// Task concurrency control
function getRunningTasksCount(): number {
  return Array.from(tasks.values()).filter(task => task.status === 'running').length;
}

function canStartNewTask(): boolean {
  return getRunningTasksCount() < MAX_CONCURRENT_TASKS;
}

// Swagger UI setup (no authentication required for docs)
const swaggerOptions = {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Deep Research API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
  },
};

app.use('/docs', swaggerUi.serve);
app.get('/docs', swaggerUi.setup(swaggerSpec, swaggerOptions));

// Serve OpenAPI spec as JSON
app.get('/openapi.json', (req: Request, res: Response) => {
  res.setHeader('Content-Type', 'application/json');
  res.json(swaggerSpec);
});

// Apply authentication and rate limiting to API routes
app.use('/api', authenticateApiKey, rateLimitMiddleware);

// Error handling middleware
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Unhandled error:', error);
  res.status(500).json(createApiError(
    'An unexpected error occurred',
    ErrorCodes.INTERNAL_ERROR
  ));
});

// Helper functions
function createApiError(message: string, code?: string, details?: any): ApiError {
  return {
    error: 'API Error',
    message,
    code,
    details,
    timestamp: new Date().toISOString(),
  };
}

function validateRequest<T>(schema: z.ZodSchema<T>, data: any): ValidationResult<T> {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: createApiError(
          'Validation failed',
          ErrorCodes.VALIDATION_ERROR,
          error.errors.map(err => ({
            path: err.path.join('.'),
            message: err.message,
            code: err.code,
          }))
        ),
      };
    }
    return {
      success: false,
      error: createApiError('Invalid request data', ErrorCodes.VALIDATION_ERROR),
    };
  }
}

function getTaskStats() {
  const stats = {
    total: tasks.size,
    pending: 0,
    running: 0,
    completed: 0,
    failed: 0,
    cancelled: 0,
  };

  for (const task of tasks.values()) {
    stats[task.status]++;
  }

  return stats;
}

function getMemoryUsage() {
  const usage = process.memoryUsage();
  return {
    used: Math.round(usage.heapUsed / 1024 / 1024), // MB
    total: Math.round(usage.heapTotal / 1024 / 1024), // MB
    percentage: Math.round((usage.heapUsed / usage.heapTotal) * 100),
  };
}

async function executeResearchTask(task: ResearchTask): Promise<void> {
  const startTime = Date.now();

  try {
    // Check if task was cancelled before starting
    if (task.status === 'cancelled') {
      return;
    }

    task.status = 'running';
    task.updatedAt = new Date();

    const onProgress = (progress: any) => {
      // Check for cancellation during progress updates
      if (task.status === 'cancelled') {
        throw new Error('Task was cancelled');
      }

      task.progress = progress;
      task.updatedAt = new Date();
    };

    // Set up timeout if specified
    const timeoutPromise = task.timeout
      ? new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Task timeout exceeded')), task.timeout! * 1000);
      })
      : null;

    const researchPromise = deepResearch({
      query: task.request.query,
      breadth: task.request.breadth,
      depth: task.request.depth,
      onProgress,
    });

    const researchResult = timeoutPromise
      ? await Promise.race([researchPromise, timeoutPromise])
      : await researchPromise;

    // Type guard to ensure we have the correct result
    if (!researchResult || typeof researchResult !== 'object' || !('learnings' in researchResult)) {
      throw new Error('Invalid research result');
    }

    const { learnings, visitedUrls, sourcedLearnings, sources } = researchResult as {
      learnings: string[];
      visitedUrls: string[];
      sourcedLearnings?: any[];
      sources?: any[];
    };

    let result: any = { learnings, visitedUrls };

    if (task.request.outputType === 'answer') {
      const answer = await writeFinalAnswer({
        prompt: task.request.query,
        learnings,
      });
      result.answer = answer;
    } else {
      const report = await writeFinalReport({
        prompt: task.request.query,
        learnings,
        visitedUrls,
        sourcedLearnings,
        sources,
      });
      result.report = report;
    }

    task.result = result;
    task.status = 'completed';
    task.completedAt = new Date();
    task.updatedAt = new Date();

    const duration = Date.now() - startTime;
    console.log(`✅ Research task ${task.id} completed successfully in ${duration}ms`);
  } catch (error) {
    if (task.status === 'cancelled') {
      console.log(`🚫 Research task ${task.id} was cancelled`);
      return;
    }

    task.status = 'failed';
    task.error = error instanceof Error ? error.message : String(error);
    task.updatedAt = new Date();

    const duration = Date.now() - startTime;
    console.error(`❌ Research task ${task.id} failed after ${duration}ms:`, error);
  }
}

// API Routes

// Health check endpoint
app.get('/api/health', (req: Request, res: Response) => {
  const healthResponse: HealthResponse = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
    memory: getMemoryUsage(),
    tasks: getTaskStats(),
  };

  res.json(healthResponse);
});

// Start a new research task
app.post('/api/research', async (req: Request, res: Response) => {
  const validation = validateRequest(ResearchRequestSchema, req.body);

  if (!validation.success) {
    return res.status(400).json(validation.error);
  }

  const requestData = validation.data!;

  // Check if we can start a new task
  if (!canStartNewTask()) {
    return res.status(429).json(createApiError(
      `Maximum concurrent tasks limit reached (${MAX_CONCURRENT_TASKS}). Please try again later.`,
      ErrorCodes.RATE_LIMIT_ERROR,
      {
        maxConcurrentTasks: MAX_CONCURRENT_TASKS,
        currentRunningTasks: getRunningTasksCount(),
      }
    ));
  }

  const taskId = uuidv4();
  const task: ResearchTask = {
    id: taskId,
    status: 'pending',
    request: {
      query: requestData.query,
      depth: requestData.depth || 2,
      breadth: requestData.breadth || 3,
      outputType: requestData.outputType || 'report',
      priority: requestData.priority || 'normal',
      metadata: requestData.metadata,
      timeout: requestData.timeout,
    },
    priority: requestData.priority || 'normal',
    timeout: requestData.timeout,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  tasks.set(taskId, task);

  // Start the research task asynchronously
  executeResearchTask(task).catch(error => {
    console.error(`Failed to execute research task ${taskId}:`, error);
  });

  const estimatedDuration = (requestData.depth || 1) * (requestData.breadth || 1) * 30;

  res.status(202).json({
    taskId,
    status: task.status,
    message: 'Research task queued successfully',
    estimatedDuration: `${estimatedDuration}s`,
    priority: task.priority,
    timeout: task.timeout,
    createdAt: task.createdAt,
    queuePosition: getRunningTasksCount(),
  });
});

// Get research task status and results
app.get('/api/research/:taskId', (req: Request, res: Response) => {
  const validation = validateRequest(TaskIdSchema, req.params);

  if (!validation.success) {
    return res.status(400).json(validation.error);
  }

  const { taskId } = validation.data!;
  const task = tasks.get(taskId);

  if (!task) {
    return res.status(404).json(createApiError('Task not found', ErrorCodes.TASK_NOT_FOUND));
  }

  const response: TaskResponse = {
    taskId: task.id,
    status: task.status,
    createdAt: task.createdAt,
    updatedAt: task.updatedAt,
    request: task.request,
  };

  if (task.progress) {
    response.progress = task.progress;
  }

  if (task.result) {
    response.result = task.result;
  }

  if (task.error) {
    response.error = task.error;
  }

  if (task.completedAt) {
    response.completedAt = task.completedAt;
    response.duration = task.completedAt.getTime() - task.createdAt.getTime();
  }

  res.json(response);
});

// Get research task progress (for real-time updates)
app.get('/api/research/:taskId/progress', (req: Request, res: Response) => {
  const validation = validateRequest(TaskIdSchema, req.params);

  if (!validation.success) {
    return res.status(400).json(validation.error);
  }

  const { taskId } = validation.data!;
  const task = tasks.get(taskId);

  if (!task) {
    return res.status(404).json(createApiError('Task not found', ErrorCodes.TASK_NOT_FOUND));
  }

  const response: ProgressResponse = {
    taskId: task.id,
    status: task.status,
    progress: task.progress || null,
    updatedAt: task.updatedAt,
  };

  res.json(response);
});

// Cancel a research task
app.post('/api/research/:taskId/cancel', (req: Request, res: Response) => {
  const validation = validateRequest(TaskIdSchema, req.params);

  if (!validation.success) {
    return res.status(400).json(validation.error);
  }

  const { taskId } = validation.data!;
  const task = tasks.get(taskId);

  if (!task) {
    return res.status(404).json(createApiError('Task not found', ErrorCodes.TASK_NOT_FOUND));
  }

  if (task.status === 'completed' || task.status === 'failed') {
    return res.status(400).json(createApiError(
      'Cannot cancel completed or failed task',
      ErrorCodes.INVALID_OPERATION
    ));
  }

  task.status = 'cancelled';
  task.updatedAt = new Date();

  res.json({
    taskId: task.id,
    status: task.status,
    message: 'Task cancelled successfully',
    updatedAt: task.updatedAt,
  });
});

// List all research tasks with pagination
app.get('/api/research', (req: Request, res: Response) => {
  const validation = validateRequest(PaginationSchema, req.query);

  if (!validation.success) {
    return res.status(400).json(validation.error);
  }

  const queryData = validation.data!;
  const { page = 1, limit = 10, status } = queryData;
  let filteredTasks = Array.from(tasks.values());

  if (status) {
    filteredTasks = filteredTasks.filter(task => task.status === status);
  }

  // Sort by creation date (newest first)
  filteredTasks.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

  const response: TaskListResponse = {
    tasks: paginatedTasks.map(task => ({
      taskId: task.id,
      status: task.status,
      query: task.request.query,
      outputType: task.request.outputType,
      priority: task.priority,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
      completedAt: task.completedAt,
      progress: task.progress,
    })),
    pagination: {
      page,
      limit,
      total: filteredTasks.length,
      totalPages: Math.ceil(filteredTasks.length / limit),
      hasNext: endIndex < filteredTasks.length,
      hasPrev: page > 1,
    },
  };

  res.json(response);
});

// Legacy endpoints for backward compatibility
app.post('/api/generate-report', async (req: Request, res: Response) => {
  const validation = validateRequest(ResearchRequestSchema.extend({
    outputType: z.literal('report').default('report'),
  }), req.body);

  if (!validation.success) {
    return res.status(400).json(validation.error);
  }

  try {
    const requestData = validation.data!;
    const { learnings, visitedUrls, sourcedLearnings, sources } = await deepResearch({
      query: requestData.query,
      breadth: requestData.breadth || 2,  // Reduced for faster testing
      depth: requestData.depth || 1,      // Reduced for faster testing
    });

    const report = await writeFinalReport({
      prompt: requestData.query,
      learnings,
      visitedUrls,
      sourcedLearnings,
      sources,
    });

    res.json({
      success: true,
      report,
      learnings,
      visitedUrls,
    });
  } catch (error) {
    console.error('Error in generate report API:', error);
    res.status(500).json(createApiError(
      'An error occurred during research',
      'RESEARCH_ERROR',
      error instanceof Error ? error.message : String(error)
    ));
  }
});

// Test endpoint for Firecrawl connection
app.get('/api/test-firecrawl', async (req: Request, res: Response) => {
  try {
    console.log('Testing Firecrawl connection...');
    console.log('Firecrawl config:', {
      apiKey: process.env.FIRECRAWL_KEY ? `${process.env.FIRECRAWL_KEY.substring(0, 5)}...` : 'NOT SET',
      baseUrl: process.env.FIRECRAWL_BASE_URL,
      concurrency: process.env.FIRECRAWL_CONCURRENCY
    });

    // Import firecrawl here to use the same instance
    const FirecrawlApp = (await import('@mendable/firecrawl-js')).default;
    const testFirecrawl = new FirecrawlApp({
      apiKey: process.env.FIRECRAWL_KEY ?? '',
      apiUrl: process.env.FIRECRAWL_BASE_URL,
    });

    // Test with a simple search
    const testResult = await testFirecrawl.search('quantum computing', {
      timeout: 10000,
      limit: 2,
      scrapeOptions: { formats: ['markdown'] },
    });

    console.log('Firecrawl test result:', {
      success: !!testResult,
      dataLength: testResult?.data?.length || 0,
      sampleData: testResult?.data?.slice(0, 1).map(item => ({
        hasUrl: !!item.url,
        hasMarkdown: !!item.markdown,
        url: item.url,
        title: item.title,
        markdownLength: item.markdown?.length || 0
      })) || []
    });

    res.json({
      success: true,
      firecrawlWorking: !!testResult,
      resultsFound: testResult?.data?.length || 0,
      sampleResult: testResult?.data?.[0] ? {
        url: testResult.data[0].url,
        title: testResult.data[0].title,
        hasMarkdown: !!testResult.data[0].markdown,
        markdownPreview: testResult.data[0].markdown?.substring(0, 200) + '...'
      } : null,
      config: {
        baseUrl: process.env.FIRECRAWL_BASE_URL,
        hasApiKey: !!process.env.FIRECRAWL_KEY
      }
    });
  } catch (error) {
    console.error('Firecrawl test error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      config: {
        baseUrl: process.env.FIRECRAWL_BASE_URL,
        hasApiKey: !!process.env.FIRECRAWL_KEY
      }
    });
  }
});

// Test endpoint for citation system
app.post('/api/test-citations', async (req: Request, res: Response) => {
  try {
    // Mock data for testing
    const mockSourcedLearnings = [
      {
        learning: "Quantum computing poses a significant threat to current cryptographic systems.",
        sourceUrls: ["https://example.com/quantum-threat", "https://example.com/crypto-analysis"]
      },
      {
        learning: "Post-quantum cryptography is being developed to address these vulnerabilities.",
        sourceUrls: ["https://example.com/post-quantum-crypto"]
      }
    ];

    const mockSources = [
      { id: 1, url: "https://example.com/quantum-threat", title: "Quantum Threat Analysis" },
      { id: 2, url: "https://example.com/crypto-analysis", title: "Cryptographic Analysis" },
      { id: 3, url: "https://example.com/post-quantum-crypto", title: "Post-Quantum Cryptography" }
    ];

    const report = await writeFinalReport({
      prompt: "Test report on quantum computing and cryptography",
      learnings: mockSourcedLearnings.map(sl => sl.learning),
      visitedUrls: mockSources.map(s => s.url),
      sourcedLearnings: mockSourcedLearnings,
      sources: mockSources,
    });

    res.json({
      success: true,
      report,
      debug: {
        sourcedLearnings: mockSourcedLearnings,
        sources: mockSources
      }
    });
  } catch (error) {
    console.error('Error in test citations:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// API Documentation endpoint (redirect to Swagger UI)
app.get('/api/docs', (req: Request, res: Response) => {
  res.redirect('/docs');
});

// API info endpoint
app.get('/api/info', (req: Request, res: Response) => {
  res.json({
    title: 'Deep Research API',
    version: '1.0.0',
    description: 'AI-powered research assistant API for performing iterative, deep research on any topic',
    documentation: {
      swagger: '/docs',
      openapi: '/openapi.json',
    },
    endpoints: {
      'POST /api/research': 'Start a new research task (async)',
      'GET /api/research/:taskId': 'Get research task status and results',
      'GET /api/research/:taskId/progress': 'Get real-time progress updates',
      'POST /api/research/:taskId/cancel': 'Cancel a running research task',
      'GET /api/research': 'List all research tasks (with pagination)',
      'GET /api/health': 'Health check endpoint',
      'POST /api/generate-report': 'Legacy endpoint for synchronous report generation',
    },
    authentication: {
      required: REQUIRE_AUTH,
      methods: ['X-API-Key header', 'Authorization: Bearer token'],
    },
    rateLimiting: {
      maxRequests: RATE_LIMIT_MAX_REQUESTS,
      windowMs: RATE_LIMIT_WINDOW,
      maxConcurrentTasks: MAX_CONCURRENT_TASKS,
    },
  });
});

// Startup tasks
function initializeServer() {
  // Start task cleanup interval
  setInterval(cleanupOldTasks, TASK_CLEANUP_INTERVAL);

  // Clean up rate limit store periodically
  setInterval(() => {
    const now = new Date();
    let cleanedCount = 0;

    for (const [clientId, data] of rateLimitStore.entries()) {
      if (now > data.resetTime) {
        rateLimitStore.delete(clientId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired rate limit entries`);
    }
  }, RATE_LIMIT_WINDOW);

  console.log('🔧 Server initialization complete');
  console.log(`📊 Configuration:`);
  console.log(`   - Authentication: ${REQUIRE_AUTH ? 'Enabled' : 'Disabled'}`);
  console.log(`   - Rate Limiting: ${RATE_LIMIT_MAX_REQUESTS} requests per ${RATE_LIMIT_WINDOW / 1000}s`);
  console.log(`   - Max Concurrent Tasks: ${MAX_CONCURRENT_TASKS}`);
  console.log(`   - Security Headers: ${ENABLE_SECURITY_HEADERS ? 'Enabled' : 'Disabled'}`);
  console.log(`   - CORS Origin: ${process.env.CORS_ORIGIN || '*'}`);
}

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');

  // Cancel all running tasks
  for (const task of tasks.values()) {
    if (task.status === 'running' || task.status === 'pending') {
      task.status = 'cancelled';
      task.updatedAt = new Date();
    }
  }

  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

// Start the server
const server = app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Deep Research API running on port ${port}`);
  console.log(`📚 Interactive API Docs: http://localhost:${port}/docs`);
  console.log(`📋 API Info: http://localhost:${port}/api/info`);
  console.log(`📄 OpenAPI Spec: http://localhost:${port}/openapi.json`);
  console.log(`❤️  Health Check: http://localhost:${port}/api/health`);
  console.log(`🌐 External Access: http://0.0.0.0:${port}`);

  initializeServer();
});

// Handle server errors
server.on('error', (error: any) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${port} is already in use`);
  } else {
    console.error('❌ Server error:', error);
  }
  process.exit(1);
});

export default app;
