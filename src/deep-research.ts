import FirecrawlApp, { SearchResponse } from '@mendable/firecrawl-js';
import { generateObject } from 'ai';
import { compact } from 'lodash-es';
import pLimit from 'p-limit';
import { z } from 'zod';

import { getModel, trimPrompt } from './ai/providers';
import { systemPrompt } from './prompt';

function log(...args: any[]) {
  console.log(...args);
}

export type ResearchProgress = {
  currentDepth: number;
  totalDepth: number;
  currentBreadth: number;
  totalBreadth: number;
  currentQuery?: string;
  totalQueries: number;
  completedQueries: number;
};

// New types for citation support
export type SourcedLearning = {
  learning: string;
  sourceUrls: string[];
};

export type CitationSource = {
  id: number;
  url: string;
  title?: string;
};

type ResearchResult = {
  learnings: string[];
  visitedUrls: string[];
  // New fields for citation support
  sourcedLearnings: SourcedLearning[];
  sources: CitationSource[];
};

// increase this if you have higher API rate limits
const ConcurrencyLimit = Number(process.env.FIRECRAWL_CONCURRENCY) || 2;

// Initialize Firecrawl with optional API key and optional base url

const firecrawl = new FirecrawlApp({
  apiKey: process.env.FIRECRAWL_KEY ?? '',
  apiUrl: process.env.FIRECRAWL_BASE_URL,
});

// take en user query, return a list of SERP queries
async function generateSerpQueries({
  query,
  numQueries = 3,
  learnings,
}: {
  query: string;
  numQueries?: number;

  // optional, if provided, the research will continue from the last learning
  learnings?: string[];
}) {
  const res = await generateObject({
    model: getModel(),
    system: systemPrompt(),
    prompt: `Given the following prompt from the user, generate a list of SERP queries to research the topic. Return a maximum of ${numQueries} queries, but feel free to return less if the original prompt is clear. Make sure each query is unique and not similar to each other: <prompt>${query}</prompt>\n\n${learnings
      ? `Here are some learnings from previous research, use them to generate more specific queries: ${learnings.join(
        '\n',
      )}`
      : ''
      }`,
    schema: z.object({
      queries: z
        .array(
          z.object({
            query: z.string().describe('The SERP query'),
            researchGoal: z
              .string()
              .describe(
                'First talk about the goal of the research that this query is meant to accomplish, then go deeper into how to advance the research once the results are found, mention additional research directions. Be as specific as possible, especially for additional research directions.',
              ),
          }),
        )
        .describe(`List of SERP queries, max of ${numQueries}`),
    }),
  });
  log(`Created ${res.object.queries.length} queries`, res.object.queries);

  return res.object.queries.slice(0, numQueries);
}

async function processSerpResult({
  query,
  result,
  numLearnings = 3,
  numFollowUpQuestions = 3,
}: {
  query: string;
  result: SearchResponse;
  numLearnings?: number;
  numFollowUpQuestions?: number;
}) {
  // Map content to URLs for source tracking
  const contentWithUrls = compact(
    result.data.map(item => ({
      content: item.markdown ? trimPrompt(item.markdown, 25_000) : null,
      url: item.url,
      title: item.title,
    }))
  ).filter(item => item.content);

  log(`Ran ${query}, found ${contentWithUrls.length} contents`);

  // Create numbered content sections with source references
  const numberedContents = contentWithUrls
    .map((item, index) => `<content source="${index + 1}" url="${item.url}" title="${item.title || ''}">\n${item.content}\n</content>`)
    .join('\n');

  const res = await generateObject({
    model: getModel(),
    abortSignal: AbortSignal.timeout(60_000),
    system: systemPrompt(),
    prompt: trimPrompt(
      `Given the following contents from a SERP search for the query <query>${query}</query>, generate a list of learnings from the contents. Return a maximum of ${numLearnings} learnings, but feel free to return less if the contents are clear. Make sure each learning is unique and not similar to each other. The learnings should be concise and to the point, as detailed and information dense as possible. Make sure to include any entities like people, places, companies, products, things, etc in the learnings, as well as any exact metrics, numbers, or dates.

IMPORTANT: For each learning, specify which source numbers (1, 2, 3, etc.) the information came from. Multiple sources can contribute to one learning.

<contents>
${numberedContents}
</contents>`,
    ),
    schema: z.object({
      learnings: z.array(z.object({
        learning: z.string().describe('The learning content'),
        sourceNumbers: z.array(z.number()).describe('Array of source numbers (1, 2, 3, etc.) that contributed to this learning'),
      })).describe(`List of learnings with source attribution, max of ${numLearnings}`),
      followUpQuestions: z
        .array(z.string())
        .describe(
          `List of follow-up questions to research the topic further, max of ${numFollowUpQuestions}`,
        ),
    }),
  });

  // Convert source numbers back to URLs
  const sourcedLearnings: SourcedLearning[] = res.object.learnings.map(item => ({
    learning: item.learning,
    sourceUrls: item.sourceNumbers
      .map(num => contentWithUrls[num - 1]?.url)
      .filter(Boolean) as string[],
  }));

  log(`Created ${sourcedLearnings.length} learnings with sources`, sourcedLearnings);

  return {
    learnings: res.object.learnings.map(l => l.learning), // Keep backward compatibility
    followUpQuestions: res.object.followUpQuestions,
    sourcedLearnings,
    contentWithUrls,
  };
}

export async function writeFinalReport({
  prompt,
  learnings,
  visitedUrls,
  sourcedLearnings,
  sources,
}: {
  prompt: string;
  learnings: string[];
  visitedUrls: string[];
  sourcedLearnings?: SourcedLearning[];
  sources?: CitationSource[];
}) {
  // Debug logging for citation system
  console.log('Citation system debug:', {
    hasSourcedLearnings: !!sourcedLearnings,
    sourcedLearningsLength: sourcedLearnings?.length || 0,
    hasSources: !!sources,
    sourcesLength: sources?.length || 0,
  });

  // If we have sourced learnings, use the new citation system
  if (sourcedLearnings && sources && sourcedLearnings.length > 0) {
    console.log('Using new citation system');
    return writeFinalReportWithCitations({
      prompt,
      sourcedLearnings,
      sources,
    });
  }

  console.log('Falling back to old citation system');

  // Fallback to old system for backward compatibility
  const learningsString = learnings
    .map(learning => `<learning>\n${learning}\n</learning>`)
    .join('\n');

  const res = await generateObject({
    model: getModel(),
    system: systemPrompt(),
    prompt: trimPrompt(
      `Given the following prompt from the user, write a final report on the topic using the learnings from research. Make it as as detailed as possible, aim for 3 or more pages, include ALL the learnings from research:\n\n<prompt>${prompt}</prompt>\n\nHere are all the learnings from previous research:\n\n<learnings>\n${learningsString}\n</learnings>`,
    ),
    schema: z.object({
      reportMarkdown: z.string().describe('Final report on the topic in Markdown'),
    }),
  });

  // Append the visited URLs section to the report
  const urlsSection = `\n\n## Sources\n\n${visitedUrls.map(url => `- ${url}`).join('\n')}`;
  return res.object.reportMarkdown + urlsSection;
}

async function writeFinalReportWithCitations({
  prompt,
  sourcedLearnings,
  sources,
}: {
  prompt: string;
  sourcedLearnings: SourcedLearning[];
  sources: CitationSource[];
}) {
  // Create a mapping from URL to citation number
  const urlToCitationMap = new Map<string, number>();
  const uniqueSources: CitationSource[] = [];

  // Deduplicate sources and create citation mapping
  sources.forEach(source => {
    if (!urlToCitationMap.has(source.url)) {
      const citationNumber = uniqueSources.length + 1;
      urlToCitationMap.set(source.url, citationNumber);
      uniqueSources.push({
        ...source,
        id: citationNumber,
      });
    }
  });

  // Create learnings with citation numbers
  const learningsWithCitations = sourcedLearnings.map(item => {
    const citationNumbers = item.sourceUrls
      .map(url => urlToCitationMap.get(url))
      .filter(Boolean) as number[];

    return {
      learning: item.learning,
      citations: citationNumbers,
    };
  });

  // Create the prompt with numbered sources
  const sourcesString = uniqueSources
    .map(source => `[${source.id}] ${source.url}${source.title ? ` - ${source.title}` : ''}`)
    .join('\n');

  const learningsString = learningsWithCitations
    .map(item => `<learning citations="[${item.citations.join(', ')}]">\n${item.learning}\n</learning>`)
    .join('\n');

  const res = await generateObject({
    model: getModel(),
    system: systemPrompt(),
    prompt: trimPrompt(
      `Given the following prompt from the user, write a final report on the topic using the learnings from research. Make it as detailed as possible, aim for 3 or more pages, include ALL the learnings from research.

IMPORTANT: Use numbered citations in your report. When you reference information from the learnings, include the citation numbers in square brackets like [1], [2], [1, 3], etc. The citation numbers are provided with each learning.

<prompt>${prompt}</prompt>

Here are the numbered sources:
<sources>
${sourcesString}
</sources>

Here are all the learnings from previous research with their citation numbers:
<learnings>
${learningsString}
</learnings>

Remember to cite sources appropriately throughout your report using the provided citation numbers.`,
    ),
    schema: z.object({
      reportMarkdown: z.string().describe('Final report on the topic in Markdown with numbered citations'),
    }),
  });

  // Append the numbered references section
  const referencesSection = `\n\n## References\n\n${uniqueSources
    .map(source => `[${source.id}] ${source.url}`)
    .join('\n')}`;

  return res.object.reportMarkdown + referencesSection;
}

export async function writeFinalAnswer({
  prompt,
  learnings,
}: {
  prompt: string;
  learnings: string[];
}) {
  const learningsString = learnings
    .map(learning => `<learning>\n${learning}\n</learning>`)
    .join('\n');

  const res = await generateObject({
    model: getModel(),
    system: systemPrompt(),
    prompt: trimPrompt(
      `Given the following prompt from the user, write a final answer on the topic using the learnings from research. Follow the format specified in the prompt. Do not yap or babble or include any other text than the answer besides the format specified in the prompt. Keep the answer as concise as possible - usually it should be just a few words or maximum a sentence. Try to follow the format specified in the prompt (for example, if the prompt is using Latex, the answer should be in Latex. If the prompt gives multiple answer choices, the answer should be one of the choices).\n\n<prompt>${prompt}</prompt>\n\nHere are all the learnings from research on the topic that you can use to help answer the prompt:\n\n<learnings>\n${learningsString}\n</learnings>`,
    ),
    schema: z.object({
      exactAnswer: z
        .string()
        .describe('The final answer, make it short and concise, just the answer, no other text'),
    }),
  });

  return res.object.exactAnswer;
}

export async function deepResearch({
  query,
  breadth,
  depth,
  learnings = [],
  visitedUrls = [],
  sourcedLearnings = [],
  sources = [],
  onProgress,
}: {
  query: string;
  breadth: number;
  depth: number;
  learnings?: string[];
  visitedUrls?: string[];
  sourcedLearnings?: SourcedLearning[];
  sources?: CitationSource[];
  onProgress?: (progress: ResearchProgress) => void;
}): Promise<ResearchResult> {
  const progress: ResearchProgress = {
    currentDepth: depth,
    totalDepth: depth,
    currentBreadth: breadth,
    totalBreadth: breadth,
    totalQueries: 0,
    completedQueries: 0,
  };

  const reportProgress = (update: Partial<ResearchProgress>) => {
    Object.assign(progress, update);
    onProgress?.(progress);
  };

  const serpQueries = await generateSerpQueries({
    query,
    learnings,
    numQueries: breadth,
  });

  reportProgress({
    totalQueries: serpQueries.length,
    currentQuery: serpQueries[0]?.query,
  });

  const limit = pLimit(ConcurrencyLimit);

  const results = await Promise.all(
    serpQueries.map(serpQuery =>
      limit(async () => {
        try {
          const result = await firecrawl.search(serpQuery.query, {
            timeout: 15000,
            limit: 5,
            scrapeOptions: { formats: ['markdown'] },
          });

          // Collect URLs from this search
          const newUrls = compact(result.data.map(item => item.url));
          const newBreadth = Math.ceil(breadth / 2);
          const newDepth = depth - 1;

          const newLearnings = await processSerpResult({
            query: serpQuery.query,
            result,
            numFollowUpQuestions: newBreadth,
          });

          // Create citation sources for new URLs
          const newSources: CitationSource[] = newLearnings.contentWithUrls
            .filter(item => item.url)
            .map((item, index) => ({
              id: sources.length + index + 1,
              url: item.url!,
              title: item.title,
            }));

          const allLearnings = [...learnings, ...newLearnings.learnings];
          const allUrls = [...visitedUrls, ...newUrls];
          const allSourcedLearnings = [...sourcedLearnings, ...newLearnings.sourcedLearnings];
          const allSources = [...sources, ...newSources];

          if (newDepth > 0) {
            log(`Researching deeper, breadth: ${newBreadth}, depth: ${newDepth}`);

            reportProgress({
              currentDepth: newDepth,
              currentBreadth: newBreadth,
              completedQueries: progress.completedQueries + 1,
              currentQuery: serpQuery.query,
            });

            const nextQuery = `
            Previous research goal: ${serpQuery.researchGoal}
            Follow-up research directions: ${newLearnings.followUpQuestions.map(q => `\n${q}`).join('')}
          `.trim();

            return deepResearch({
              query: nextQuery,
              breadth: newBreadth,
              depth: newDepth,
              learnings: allLearnings,
              visitedUrls: allUrls,
              sourcedLearnings: allSourcedLearnings,
              sources: allSources,
              onProgress,
            });
          } else {
            reportProgress({
              currentDepth: 0,
              completedQueries: progress.completedQueries + 1,
              currentQuery: serpQuery.query,
            });
            return {
              learnings: allLearnings,
              visitedUrls: allUrls,
              sourcedLearnings: allSourcedLearnings,
              sources: allSources,
            };
          }
        } catch (e: any) {
          if (e.message && e.message.includes('Timeout')) {
            log(`Timeout error running query: ${serpQuery.query}: `, e);
          } else {
            log(`Error running query: ${serpQuery.query}: `, e);
          }
          return {
            learnings: [],
            visitedUrls: [],
            sourcedLearnings: [],
            sources: [],
          };
        }
      }),
    ),
  );

  // Combine all results
  const allLearnings = [...new Set(results.flatMap(r => r.learnings))];
  const allUrls = [...new Set(results.flatMap(r => r.visitedUrls))];
  const allSourcedLearnings = results.flatMap(r => r.sourcedLearnings || []);
  const allSources = results.flatMap(r => r.sources || []);

  return {
    learnings: allLearnings,
    visitedUrls: allUrls,
    sourcedLearnings: allSourcedLearnings,
    sources: allSources,
  };
}
