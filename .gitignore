# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Output files
output.md
report.md
answer.md

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
bun.lockb