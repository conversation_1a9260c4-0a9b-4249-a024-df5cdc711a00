# Deep Research API Usage Guide for AI Agents

This guide explains how AI agents can interact with the Deep Research API hosted at `https://deep.w-post.com`.

## 🔑 Authentication

The API requires authentication using an API key:

```bash
# Include in headers
X-API-Key: 12345
# OR
Authorization: Bearer 12345
```

## 🚀 Quick Start: Asynchronous Research (Recommended)

### Step 1: Start a Research Task

```bash
curl -X POST https://deep.w-post.com/api/research \
  -H "Content-Type: application/json" \
  -H "X-API-Key: 12345" \
  -d '{
    "query": "Latest developments in quantum computing",
    "depth": 3,
    "breadth": 4,
    "outputType": "report",
    "priority": "normal"
  }'
```

**Response:**
```json
{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "status": "pending",
  "message": "Research task queued successfully",
  "estimatedDuration": "360s",
  "priority": "normal",
  "createdAt": "2025-01-02T10:00:00Z",
  "queuePosition": 1
}
```

### Step 2: Check Task Status and Get Results

```bash
curl -X GET https://deep.w-post.com/api/research/550e8400-e29b-41d4-a716-************ \
  -H "X-API-Key: 12345"
```

**Response (when completed):**
```json
{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "result": {
    "report": "# Quantum Computing Developments\n\n## Overview\n...",
    "learnings": [
      "IBM announced a 1000-qubit quantum processor in 2023",
      "Google achieved quantum supremacy with Sycamore processor"
    ],
    "visitedUrls": [
      "https://www.ibm.com/quantum",
      "https://ai.google/quantum"
    ]
  },
  "request": {
    "query": "Latest developments in quantum computing",
    "depth": 3,
    "breadth": 4,
    "outputType": "report"
  },
  "createdAt": "2025-01-02T10:00:00Z",
  "completedAt": "2025-01-02T10:06:30Z",
  "duration": 390000
}
```

## 📊 Real-time Progress Monitoring

```bash
curl -X GET https://deep.w-post.com/api/research/550e8400-e29b-41d4-a716-************/progress \
  -H "X-API-Key: 12345"
```

**Response:**
```json
{
  "taskId": "550e8400-e29b-41d4-a716-************",
  "status": "running",
  "progress": {
    "currentDepth": 2,
    "totalDepth": 3,
    "currentBreadth": 3,
    "totalBreadth": 4,
    "currentQuery": "quantum computing applications",
    "totalQueries": 12,
    "completedQueries": 8
  },
  "updatedAt": "2025-01-02T10:04:15Z"
}
```

## 📝 Request Parameters

### Research Request Schema

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `query` | string | ✅ | - | Research query (1-1000 chars) |
| `depth` | integer | ❌ | 3 | Research depth (1-10) |
| `breadth` | integer | ❌ | 3 | Research breadth (1-20) |
| `outputType` | enum | ❌ | "report" | "report" or "answer" |
| `priority` | enum | ❌ | "normal" | "low", "normal", "high" |
| `timeout` | integer | ❌ | - | Timeout in seconds (60-3600) |
| `metadata` | object | ❌ | - | Custom metadata |

### Output Types

- **"report"**: Detailed markdown report (3+ pages)
- **"answer"**: Concise answer to the query

## 🔄 Task Management

### List All Tasks

```bash
curl -X GET "https://deep.w-post.com/api/research?page=1&limit=10&status=completed" \
  -H "X-API-Key: 12345"
```

### Cancel a Running Task

```bash
curl -X POST https://deep.w-post.com/api/research/550e8400-e29b-41d4-a716-************/cancel \
  -H "X-API-Key: 12345"
```

## 🏥 Health Check

```bash
curl -X GET https://deep.w-post.com/api/health \
  -H "X-API-Key: 12345"
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-02T10:00:00Z",
  "version": "1.0.0",
  "uptime": 86400,
  "memory": {
    "used": 512,
    "total": 2048,
    "percentage": 25
  },
  "tasks": {
    "total": 150,
    "pending": 2,
    "running": 3,
    "completed": 140,
    "failed": 3,
    "cancelled": 2
  }
}
```

## 🎯 AI Agent Integration Examples

### Python Example

```python
import requests
import time
import json

class DeepResearchClient:
    def __init__(self, base_url="https://deep.w-post.com", api_key="12345"):
        self.base_url = base_url
        self.headers = {"X-API-Key": api_key, "Content-Type": "application/json"}
    
    def start_research(self, query, output_type="report", depth=3, breadth=4):
        """Start a research task"""
        payload = {
            "query": query,
            "depth": depth,
            "breadth": breadth,
            "outputType": output_type
        }
        
        response = requests.post(
            f"{self.base_url}/api/research",
            headers=self.headers,
            json=payload
        )
        return response.json()
    
    def get_results(self, task_id):
        """Get research results"""
        response = requests.get(
            f"{self.base_url}/api/research/{task_id}",
            headers=self.headers
        )
        return response.json()
    
    def wait_for_completion(self, task_id, check_interval=10):
        """Wait for task completion and return results"""
        while True:
            result = self.get_results(task_id)
            
            if result["status"] == "completed":
                return result["result"]
            elif result["status"] == "failed":
                raise Exception(f"Research failed: {result.get('error', 'Unknown error')}")
            
            time.sleep(check_interval)

# Usage
client = DeepResearchClient()

# Start research
task = client.start_research("AI safety research trends")
print(f"Started task: {task['taskId']}")

# Wait for completion
results = client.wait_for_completion(task["taskId"])
print(f"Report: {results['report'][:200]}...")
```

### JavaScript/Node.js Example

```javascript
class DeepResearchClient {
    constructor(baseUrl = "https://deep.w-post.com", apiKey = "12345") {
        this.baseUrl = baseUrl;
        this.headers = {
            "X-API-Key": apiKey,
            "Content-Type": "application/json"
        };
    }

    async startResearch(query, options = {}) {
        const payload = {
            query,
            depth: options.depth || 3,
            breadth: options.breadth || 4,
            outputType: options.outputType || "report"
        };

        const response = await fetch(`${this.baseUrl}/api/research`, {
            method: "POST",
            headers: this.headers,
            body: JSON.stringify(payload)
        });

        return await response.json();
    }

    async getResults(taskId) {
        const response = await fetch(`${this.baseUrl}/api/research/${taskId}`, {
            headers: this.headers
        });

        return await response.json();
    }

    async waitForCompletion(taskId, checkInterval = 10000) {
        while (true) {
            const result = await this.getResults(taskId);
            
            if (result.status === "completed") {
                return result.result;
            } else if (result.status === "failed") {
                throw new Error(`Research failed: ${result.error || "Unknown error"}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, checkInterval));
        }
    }
}

// Usage
const client = new DeepResearchClient();

async function doResearch() {
    const task = await client.startResearch("Climate change solutions");
    console.log(`Started task: ${task.taskId}`);
    
    const results = await client.waitForCompletion(task.taskId);
    console.log(`Report: ${results.report.substring(0, 200)}...`);
}
```

## ⚠️ Error Handling

### Common HTTP Status Codes

- **200**: Success
- **202**: Task accepted (for async operations)
- **400**: Bad request (validation error)
- **401**: Unauthorized (invalid API key)
- **404**: Task not found
- **429**: Rate limit exceeded
- **500**: Internal server error

### Error Response Format

```json
{
  "error": "Validation failed",
  "message": "Query is required",
  "code": "VALIDATION_ERROR",
  "details": [
    {
      "path": "query",
      "message": "Query is required",
      "code": "invalid_type"
    }
  ],
  "timestamp": "2025-01-02T10:00:00Z"
}
```

## 📚 API Documentation

- **Swagger UI**: https://deep.w-post.com/docs
- **OpenAPI Spec**: https://deep.w-post.com/openapi.json
- **API Info**: https://deep.w-post.com/api/info

## 🔧 Rate Limits

- **100 requests per 15 minutes** per API key
- **10 concurrent tasks** maximum
- Use appropriate delays between requests

## 💡 Best Practices for AI Agents

1. **Always use async workflow** (`/api/research`) instead of legacy endpoints
2. **Implement proper error handling** and retry logic
3. **Monitor task progress** for long-running research
4. **Cache results** when appropriate to avoid duplicate research
5. **Use appropriate depth/breadth** based on research complexity
6. **Include meaningful metadata** for task tracking
7. **Respect rate limits** and implement backoff strategies

## 🎯 Use Cases

- **Research Reports**: Comprehensive analysis on any topic
- **Fact Checking**: Quick answers with source verification
- **Market Research**: Industry trends and competitive analysis
- **Academic Research**: Literature review and synthesis
- **News Analysis**: Current events and trend analysis
