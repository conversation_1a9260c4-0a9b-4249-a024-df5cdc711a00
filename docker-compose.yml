version: '3.8'

services:
  deep-research-api:
    container_name: deep-research-api
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3051:3051"
    env_file:
      - .env.local
    environment:
      - NODE_ENV=production
      - PORT=3051
      - CORS_ORIGIN=*
      - ENABLE_SECURITY_HEADERS=true
      - TRUST_PROXY=true
    volumes:
      # Only mount specific directories for production
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - deep-research-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3051/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Caddy reverse proxy with automatic HTTPS
  caddy:
    image: caddy:2-alpine
    container_name: deep-research-caddy
    ports:
      - "80:80"
      - "443:443"
      - "2019:2019"  # Admin API
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - caddy_logs:/var/log/caddy
    depends_on:
      - deep-research-api
    networks:
      - deep-research-network
    restart: unless-stopped
    profiles:
      - production
    environment:
      - CADDY_ADMIN=0.0.0.0:2019

networks:
  deep-research-network:
    driver: bridge

volumes:
  logs:
    driver: local
  caddy_data:
    driver: local
  caddy_config:
    driver: local
  caddy_logs:
    driver: local
