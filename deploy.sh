#!/bin/bash

# Deep Research API Deployment Script
# This script helps deploy the Deep Research API for external access

set -e

echo "🚀 Deep Research API Deployment Script"
echo "======================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local from .env.example..."
    cp .env.example .env.local
    echo "⚠️  Please edit .env.local with your API keys before continuing."
    echo "   Required: FIRECRAWL_KEY, OPENAI_KEY (or FIREWORKS_KEY)"
    read -p "Press Enter after configuring .env.local..."
fi

# Function to check if required environment variables are set
check_env_vars() {
    local missing_vars=()
    
    # Source the .env.local file
    set -a
    source .env.local
    set +a
    
    # Check required variables
    if [ -z "$FIRECRAWL_KEY" ]; then
        missing_vars+=("FIRECRAWL_KEY")
    fi
    
    if [ -z "$OPENAI_KEY" ] && [ -z "$FIREWORKS_KEY" ]; then
        missing_vars+=("OPENAI_KEY or FIREWORKS_KEY")
    fi
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        echo "❌ Missing required environment variables:"
        printf '   - %s\n' "${missing_vars[@]}"
        echo "Please configure these in .env.local"
        exit 1
    fi
    
    echo "✅ Environment variables configured"
}

# Function to deploy development version
deploy_dev() {
    echo "🔧 Deploying development version..."
    
    # Build and start the container
    docker-compose up --build -d deep-research-api
    
    echo "⏳ Waiting for service to start..."
    sleep 10
    
    # Check if service is healthy
    if curl -f http://localhost:3051/api/health > /dev/null 2>&1; then
        echo "✅ Service is running and healthy!"
        echo "📚 API Documentation: http://localhost:3051/docs"
        echo "❤️  Health Check: http://localhost:3051/api/health"
        echo "🌐 External Access: http://$(hostname -I | awk '{print $1}'):3051"
    else
        echo "❌ Service failed to start properly"
        echo "📋 Checking logs..."
        docker-compose logs deep-research-api
        exit 1
    fi
}

# Function to deploy production version with nginx
deploy_prod() {
    echo "🏭 Deploying production version with nginx..."
    
    # Create logs directory
    mkdir -p logs
    
    # Build and start all services
    docker-compose --profile production up --build -d
    
    echo "⏳ Waiting for services to start..."
    sleep 15
    
    # Check if services are healthy
    if curl -f http://localhost/api/health > /dev/null 2>&1; then
        echo "✅ Production services are running and healthy!"
        echo "📚 API Documentation: http://localhost/docs"
        echo "❤️  Health Check: http://localhost/api/health"
        echo "🌐 External Access: http://$(hostname -I | awk '{print $1}')"
        echo ""
        echo "⚠️  For HTTPS in production:"
        echo "   1. Obtain SSL certificates"
        echo "   2. Place them in ./ssl/ directory"
        echo "   3. Uncomment HTTPS server block in nginx.conf"
        echo "   4. Update server_name in nginx.conf"
        echo "   5. Restart: docker-compose --profile production restart nginx"
    else
        echo "❌ Services failed to start properly"
        echo "📋 Checking logs..."
        docker-compose logs
        exit 1
    fi
}

# Function to show status
show_status() {
    echo "📊 Service Status:"
    docker-compose ps
    echo ""
    echo "📋 Recent logs:"
    docker-compose logs --tail=20
}

# Function to stop services
stop_services() {
    echo "🛑 Stopping services..."
    docker-compose down
    echo "✅ Services stopped"
}

# Function to show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev      Deploy development version (API only)"
    echo "  prod     Deploy production version (API + nginx)"
    echo "  status   Show service status and logs"
    echo "  stop     Stop all services"
    echo "  help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev     # Deploy for development"
    echo "  $0 prod    # Deploy for production"
    echo "  $0 status  # Check service status"
    echo "  $0 stop    # Stop all services"
}

# Main script logic
case "${1:-dev}" in
    "dev")
        check_env_vars
        deploy_dev
        ;;
    "prod")
        check_env_vars
        deploy_prod
        ;;
    "status")
        show_status
        ;;
    "stop")
        stop_services
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_help
        exit 1
        ;;
esac
