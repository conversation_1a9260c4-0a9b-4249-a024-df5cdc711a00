{"name": "Deep Research - Extract Markdown Results", "nodes": [{"parameters": {}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "api_base_url", "value": "https://deep.w-post.com"}, {"name": "research_query", "value": "What are the latest trends in artificial intelligence?"}], "number": [{"name": "depth", "value": 2}, {"name": "breadth", "value": 3}]}}, "id": "config", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/research", "method": "POST", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $node.Configuration.json.research_query }}"}, {"name": "depth", "value": "={{ $node.Configuration.json.depth }}"}, {"name": "breadth", "value": "={{ $node.Configuration.json.breadth }}"}, {"name": "outputType", "value": "report"}]}, "options": {"timeout": 30000}}, "id": "create-research-task", "name": "1. Create Research Task", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-initial", "name": "Wait 30s", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/research/{{ $node['1. Create Research Task'].json.taskId }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {"timeout": 15000}}, "id": "check-status-1", "name": "2. Check Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "completed"}]}}, "id": "check-if-completed", "name": "Is Completed?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-more", "name": "Wait 30s More", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 500]}, {"parameters": {"values": {"string": [{"name": "task_id", "value": "={{ $node['1. Create Research Task'].json.taskId }}"}, {"name": "status", "value": "={{ $json.status }}"}, {"name": "markdown_report", "value": "={{ $json.result.reportMarkdown }}"}, {"name": "short_answer", "value": "={{ $json.result.answer }}"}, {"name": "learnings_count", "value": "={{ $json.result.learnings.length }}"}, {"name": "sources_count", "value": "={{ $json.result.visitedUrls.length }}"}], "array": [{"name": "learnings", "value": "={{ $json.result.learnings }}"}, {"name": "sources", "value": "={{ $json.result.visitedUrls }}"}]}}, "id": "extract-results", "name": "3. Extract Results", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1560, 200]}], "connections": {"Start": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "1. Create Research Task", "type": "main", "index": 0}]]}, "1. Create Research Task": {"main": [[{"node": "Wait 30s", "type": "main", "index": 0}]]}, "Wait 30s": {"main": [[{"node": "2. Check Status", "type": "main", "index": 0}]]}, "2. Check Status": {"main": [[{"node": "Is Completed?", "type": "main", "index": 0}]]}, "Is Completed?": {"main": [[{"node": "3. Extract Results", "type": "main", "index": 0}], [{"node": "Wait 30s More", "type": "main", "index": 0}]]}, "Wait 30s More": {"main": [[{"node": "2. Check Status", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"instanceId": "deep-research-markdown-extraction"}, "id": "research-markdown-extraction-workflow", "tags": [{"createdAt": "2025-06-25T00:00:00.000Z", "updatedAt": "2025-06-25T00:00:00.000Z", "id": "research", "name": "research"}, {"createdAt": "2025-06-25T00:00:00.000Z", "updatedAt": "2025-06-25T00:00:00.000Z", "id": "markdown", "name": "markdown"}]}