{"name": "Deep Research API - Test Connectivity", "nodes": [{"parameters": {}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "api_base_url", "value": "https://deep.w-post.com"}, {"name": "test_query", "value": "What is Docker containerization?"}]}}, "id": "config", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/health", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {"timeout": 10000}}, "id": "health-check", "name": "1. Health Check", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/info", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {"timeout": 10000}}, "id": "api-info", "name": "2. API Info", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/research", "method": "POST", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $node.Configuration.json.test_query }}"}, {"name": "depth", "value": 1}, {"name": "breadth", "value": 2}, {"name": "outputType", "value": "answer"}]}, "options": {"timeout": 15000}}, "id": "test-research", "name": "3. Test Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 400]}, {"parameters": {"amount": 5, "unit": "seconds"}, "id": "wait-for-task", "name": "Wait for Task", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/research/{{ $node['3. Test Research'].json.taskId }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {"timeout": 10000}}, "id": "check-task-status", "name": "4. Check Task Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 400]}, {"parameters": {"values": {"string": [{"name": "health_status", "value": "={{ $node['1. Health Check'].json.status }}"}, {"name": "api_version", "value": "={{ $node['2. API Info'].json.version }}"}, {"name": "api_title", "value": "={{ $node['2. API Info'].json.title }}"}, {"name": "task_id", "value": "={{ $node['3. Test Research'].json.taskId }}"}, {"name": "task_status", "value": "={{ $node['4. Check Task Status'].json.status }}"}, {"name": "test_result", "value": "✅ All connectivity tests passed! API is working correctly."}], "boolean": [{"name": "all_tests_passed", "value": true}]}}, "id": "format-results", "name": "5. Format Test Results", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Start": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "1. Health Check", "type": "main", "index": 0}, {"node": "2. API Info", "type": "main", "index": 0}, {"node": "3. Test Research", "type": "main", "index": 0}]]}, "3. Test Research": {"main": [[{"node": "Wait for Task", "type": "main", "index": 0}]]}, "Wait for Task": {"main": [[{"node": "4. Check Task Status", "type": "main", "index": 0}]]}, "1. Health Check": {"main": [[{"node": "5. Format Test Results", "type": "main", "index": 0}]]}, "2. API Info": {"main": [[{"node": "5. Format Test Results", "type": "main", "index": 0}]]}, "4. Check Task Status": {"main": [[{"node": "5. Format Test Results", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"instanceId": "deep-research-api-test"}, "id": "test-connectivity-workflow", "tags": [{"createdAt": "2025-06-25T00:00:00.000Z", "updatedAt": "2025-06-25T00:00:00.000Z", "id": "test", "name": "test"}, {"createdAt": "2025-06-25T00:00:00.000Z", "updatedAt": "2025-06-25T00:00:00.000Z", "id": "connectivity", "name": "connectivity"}]}