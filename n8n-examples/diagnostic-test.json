{"name": "Deep Research API - Diagnostic Test", "nodes": [{"parameters": {}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "api_base_url", "value": "https://deep.w-post.com"}, {"name": "test_query", "value": "What is containerization technology?"}]}}, "id": "config", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/docs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"}]}, "options": {"timeout": 15000, "response": {"response": {"fullResponse": true}}}}, "id": "test-docs", "name": "Test 1: /docs (No Auth)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/health", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {"timeout": 15000, "response": {"response": {"fullResponse": true}}}}, "id": "test-health", "name": "Test 2: /api/health", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/research", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}]}, "options": {"timeout": 15000, "response": {"response": {"fullResponse": true}}}}, "id": "test-list-research", "name": "Test 3: GET /api/research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 400]}, {"parameters": {"url": "={{ $node.Configuration.json.api_base_url }}/api/research", "method": "POST", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Accept", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $node.Configuration.json.test_query }}"}, {"name": "depth", "value": 1}, {"name": "breadth", "value": 2}, {"name": "outputType", "value": "answer"}]}, "options": {"timeout": 30000, "response": {"response": {"fullResponse": true}}}}, "id": "test-create-research", "name": "Test 4: POST /api/research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 500]}], "connections": {"Start": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "Test 1: /docs (No Auth)", "type": "main", "index": 0}, {"node": "Test 2: /api/health", "type": "main", "index": 0}, {"node": "Test 3: GET /api/research", "type": "main", "index": 0}, {"node": "Test 4: POST /api/research", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"instanceId": "deep-research-api-diagnostic"}, "id": "diagnostic-test-workflow", "tags": [{"createdAt": "2025-06-25T00:00:00.000Z", "updatedAt": "2025-06-25T00:00:00.000Z", "id": "diagnostic", "name": "diagnostic"}]}