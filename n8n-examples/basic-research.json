{"name": "Deep Research - Basic Workflow", "nodes": [{"parameters": {}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "research_query", "value": "What are the latest developments in artificial intelligence?"}, {"name": "api_base_url", "value": "https://deep.w-post.com"}], "number": [{"name": "depth", "value": 3}, {"name": "breadth", "value": 4}]}}, "id": "set-variables", "name": "Set Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "={{ $node['Set Variables'].json.api_base_url }}/api/research", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $node['Set Variables'].json.research_query }}"}, {"name": "depth", "value": "={{ $node['Set Variables'].json.depth }}"}, {"name": "breadth", "value": "={{ $node['Set Variables'].json.breadth }}"}, {"name": "outputType", "value": "report"}]}, "options": {}}, "id": "start-research", "name": "Start Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"values": {"string": [{"name": "task_id", "value": "={{ $json.taskId }}"}, {"name": "status", "value": "={{ $json.status }}"}]}}, "id": "extract-task-id", "name": "Extract Task ID", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"amount": 10, "unit": "seconds"}, "id": "initial-wait", "name": "Initial Wait", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"url": "={{ $node['Set Variables'].json.api_base_url }}/api/research/{{ $node['Extract Task ID'].json.task_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "options": {}}, "id": "check-status", "name": "Check Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "completed"}]}}, "id": "is-completed", "name": "Is Completed?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-and-retry", "name": "Wait and Retry", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 500]}, {"parameters": {"values": {"string": [{"name": "research_query", "value": "={{ $node['Set Variables'].json.research_query }}"}, {"name": "task_id", "value": "={{ $json.taskId }}"}, {"name": "status", "value": "={{ $json.status }}"}, {"name": "report", "value": "={{ $json.result.report }}"}, {"name": "learnings_count", "value": "={{ $json.result.learnings.length }}"}, {"name": "urls_count", "value": "={{ $json.result.visitedUrls.length }}"}, {"name": "duration", "value": "={{ $json.duration }}"}]}}, "id": "format-results", "name": "Format Results", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "failed"}]}}, "id": "check-failed", "name": "Check Failed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 400]}, {"parameters": {"message": "Research task failed: {{ $json.error }}", "options": {}}, "id": "error-output", "name": "Error Output", "type": "n8n-nodes-base.stopAndError", "typeVersion": 1, "position": [2000, 500]}], "connections": {"Start": {"main": [[{"node": "Set Variables", "type": "main", "index": 0}]]}, "Set Variables": {"main": [[{"node": "Start Research", "type": "main", "index": 0}]]}, "Start Research": {"main": [[{"node": "Extract Task ID", "type": "main", "index": 0}]]}, "Extract Task ID": {"main": [[{"node": "Initial Wait", "type": "main", "index": 0}]]}, "Initial Wait": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Is Completed?", "type": "main", "index": 0}]]}, "Is Completed?": {"main": [[{"node": "Format Results", "type": "main", "index": 0}], [{"node": "Check Failed", "type": "main", "index": 0}]]}, "Wait and Retry": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Check Failed": {"main": [[{"node": "Error Output", "type": "main", "index": 0}], [{"node": "Wait and Retry", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"instanceId": "deep-research-api"}, "id": "basic-research-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "research", "name": "research"}]}