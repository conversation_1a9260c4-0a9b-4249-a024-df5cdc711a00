{"name": "Deep Research - Webhook with Notifications", "nodes": [{"parameters": {"httpMethod": "POST", "path": "research", "responseMode": "responseNode", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "research-webhook"}, {"parameters": {"values": {"string": [{"name": "query", "value": "={{ $json.body.query || 'Latest technology trends' }}"}, {"name": "api_base_url", "value": "https://deep.w-post.com"}, {"name": "slack_webhook", "value": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"}, {"name": "requester_email", "value": "={{ $json.body.email || '<EMAIL>' }}"}], "number": [{"name": "depth", "value": "={{ $json.body.depth || 3 }}"}, {"name": "breadth", "value": "={{ $json.body.breadth || 4 }}"}]}}, "id": "extract-params", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"url": "={{ $json.api_base_url }}/api/research", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $json.query }}"}, {"name": "depth", "value": "={{ $json.depth }}"}, {"name": "breadth", "value": "={{ $json.breadth }}"}, {"name": "outputType", "value": "report"}, {"name": "priority", "value": "high"}]}, "options": {}}, "id": "start-research", "name": "Start Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"values": {"string": [{"name": "task_id", "value": "={{ $json.taskId }}"}, {"name": "status", "value": "={{ $json.status }}"}, {"name": "estimated_duration", "value": "={{ $json.estimatedDuration }}"}]}}, "id": "store-task-info", "name": "Store Task Info", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"Research task started successfully\",\n  \"taskId\": \"{{ $json.task_id }}\",\n  \"status\": \"{{ $json.status }}\",\n  \"estimatedDuration\": \"{{ $json.estimated_duration }}\",\n  \"query\": \"{{ $node['Extract Parameters'].json.query }}\"\n}", "options": {}}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"url": "={{ $node['Extract Parameters'].json.slack_webhook }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "🔍 Research Started: {{ $node['Extract Parameters'].json.query }}"}, {"name": "attachments", "value": "[\n  {\n    \"color\": \"#36a64f\",\n    \"fields\": [\n      {\n        \"title\": \"Task ID\",\n        \"value\": \"{{ $json.task_id }}\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Estimated Duration\",\n        \"value\": \"{{ $json.estimated_duration }}\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Depth\",\n        \"value\": \"{{ $node['Extract Parameters'].json.depth }}\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Breadth\",\n        \"value\": \"{{ $node['Extract Parameters'].json.breadth }}\",\n        \"short\": true\n      }\n    ]\n  }\n]"}]}, "options": {}}, "id": "notify-start", "name": "Notify Start", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-initial", "name": "Wait Initial", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"url": "={{ $node['Extract Parameters'].json.api_base_url }}/api/research/{{ $json.task_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "check-progress", "name": "Check Progress", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1780, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "completed"}]}}, "id": "is-completed", "name": "Is Completed?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"url": "={{ $node['Extract Parameters'].json.slack_webhook }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "✅ Research Completed: {{ $node['Extract Parameters'].json.query }}"}, {"name": "attachments", "value": "[\n  {\n    \"color\": \"#36a64f\",\n    \"fields\": [\n      {\n        \"title\": \"Task ID\",\n        \"value\": \"{{ $json.taskId }}\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Duration\",\n        \"value\": \"{{ Math.round($json.duration / 1000) }}s\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Learnings\",\n        \"value\": \"{{ $json.result.learnings.length }} findings\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Sources\",\n        \"value\": \"{{ $json.result.visitedUrls.length }} URLs\",\n        \"short\": true\n      }\n    ]\n  }\n]"}]}, "options": {}}, "id": "notify-completion", "name": "Notify Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2220, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $node['Extract Parameters'].json.requester_email }}", "subject": "Research Report: {{ $node['Extract Parameters'].json.query }}", "emailType": "html", "message": "<h1>Research Report</h1>\n\n<p><strong>Query:</strong> {{ $node['Extract Parameters'].json.query }}</p>\n<p><strong>Completed:</strong> {{ $now.format('YYYY-MM-DD HH:mm:ss') }}</p>\n<p><strong>Duration:</strong> {{ Math.round($json.duration / 1000) }} seconds</p>\n<p><strong>Sources:</strong> {{ $json.result.visitedUrls.length }} URLs analyzed</p>\n\n<h2>Report</h2>\n<div style=\"white-space: pre-wrap; font-family: Arial, sans-serif;\">{{ $json.result.report }}</div>\n\n<h2>Key Learnings</h2>\n<ul>\n{% for learning in $json.result.learnings %}\n<li>{{ learning }}</li>\n{% endfor %}\n</ul>\n\n<hr>\n<p><em>Generated by Deep Research API</em></p>", "options": {}}, "id": "send-email-report", "name": "Send Email Report", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2440, 200]}, {"parameters": {"amount": 60, "unit": "seconds"}, "id": "wait-retry", "name": "Wait Retry", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1780, 500]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "failed"}]}}, "id": "check-failed", "name": "Check Failed", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [2220, 400]}, {"parameters": {"url": "={{ $node['Extract Parameters'].json.slack_webhook }}", "sendBody": true, "bodyParameters": {"parameters": [{"name": "text", "value": "❌ Research Failed: {{ $node['Extract Parameters'].json.query }}"}, {"name": "attachments", "value": "[\n  {\n    \"color\": \"#ff0000\",\n    \"fields\": [\n      {\n        \"title\": \"Task ID\",\n        \"value\": \"{{ $json.taskId }}\",\n        \"short\": true\n      },\n      {\n        \"title\": \"Error\",\n        \"value\": \"{{ $json.error }}\",\n        \"short\": false\n      }\n    ]\n  }\n]"}]}, "options": {}}, "id": "notify-failure", "name": "Notify Failure", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2440, 500]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Start Research", "type": "main", "index": 0}]]}, "Start Research": {"main": [[{"node": "Store Task Info", "type": "main", "index": 0}]]}, "Store Task Info": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Webhook Response": {"main": [[{"node": "Notify Start", "type": "main", "index": 0}]]}, "Notify Start": {"main": [[{"node": "Wait Initial", "type": "main", "index": 0}]]}, "Wait Initial": {"main": [[{"node": "Check Progress", "type": "main", "index": 0}]]}, "Check Progress": {"main": [[{"node": "Is Completed?", "type": "main", "index": 0}]]}, "Is Completed?": {"main": [[{"node": "Notify Completion", "type": "main", "index": 0}], [{"node": "Check Failed", "type": "main", "index": 0}]]}, "Notify Completion": {"main": [[{"node": "Send Email Report", "type": "main", "index": 0}]]}, "Wait Retry": {"main": [[{"node": "Check Progress", "type": "main", "index": 0}]]}, "Check Failed": {"main": [[{"node": "Notify Failure", "type": "main", "index": 0}], [{"node": "Wait Retry", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"instanceId": "deep-research-api"}, "id": "webhook-research-notifications", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "research", "name": "research"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "webhook", "name": "webhook"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "notifications", "name": "notifications"}]}