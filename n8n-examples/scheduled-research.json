{"name": "Deep Research - Scheduled Daily Reports", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9 * * 1-5"}]}}, "id": "schedule-trigger", "name": "Daily at 9 AM (Weekdays)", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "api_base_url", "value": "https://deep.w-post.com"}, {"name": "email_recipient", "value": "<EMAIL>"}], "array": [{"name": "research_topics", "value": ["Latest AI developments and breakthroughs", "Cryptocurrency market trends and analysis", "Climate change and renewable energy news", "Technology startup funding and acquisitions"]}]}}, "id": "config", "name": "Configuration", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"fieldToSplitOut": "research_topics", "options": {}}, "id": "split-topics", "name": "Split Topics", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"url": "={{ $node['Configuration'].json.api_base_url }}/api/research", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "query", "value": "={{ $json.research_topics }}"}, {"name": "depth", "value": 2}, {"name": "breadth", "value": 3}, {"name": "outputType", "value": "report"}, {"name": "priority", "value": "normal"}]}, "options": {}}, "id": "start-research", "name": "Start Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300]}, {"parameters": {"values": {"string": [{"name": "task_id", "value": "={{ $json.taskId }}"}, {"name": "topic", "value": "={{ $node['Split Topics'].json.research_topics }}"}]}}, "id": "store-task-info", "name": "Store Task Info", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"amount": 30, "unit": "seconds"}, "id": "wait-for-completion", "name": "Wait for Completion", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"url": "={{ $node['Configuration'].json.api_base_url }}/api/research/{{ $json.task_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "options": {}}, "id": "get-results", "name": "Get Results", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1560, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.status }}", "operation": "equal", "value2": "completed"}]}}, "id": "check-completion", "name": "Check Completion", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"values": {"string": [{"name": "topic", "value": "={{ $json.topic }}"}, {"name": "report", "value": "={{ $json.result.report }}"}, {"name": "summary", "value": "Research completed with {{ $json.result.learnings.length }} key findings from {{ $json.result.visitedUrls.length }} sources."}]}}, "id": "format-report", "name": "Format Report", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"amount": 60, "unit": "seconds"}, "id": "retry-wait", "name": "Retry Wait", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1560, 500]}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "reports", "options": {}}, "id": "aggregate-reports", "name": "Aggregate Reports", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2220, 200]}, {"parameters": {"values": {"string": [{"name": "email_subject", "value": "Daily Research Report - {{ $now.format('YYYY-MM-DD') }}"}, {"name": "email_body", "value": "# Daily Research Report\n\n**Date:** {{ $now.format('YYYY-MM-DD') }}\n\n{% for report in $json.reports %}\n## {{ report.topic }}\n\n{{ report.summary }}\n\n{{ report.report }}\n\n---\n\n{% endfor %}\n\n*Generated by Deep Research API*"}]}}, "id": "prepare-email", "name": "Prepare <PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [2440, 200]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $node['Configuration'].json.email_recipient }}", "subject": "={{ $json.email_subject }}", "emailType": "html", "message": "={{ $json.email_body }}", "options": {}}, "id": "send-email", "name": "Send Email Report", "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [2660, 200]}], "connections": {"Daily at 9 AM (Weekdays)": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "Split Topics", "type": "main", "index": 0}]]}, "Split Topics": {"main": [[{"node": "Start Research", "type": "main", "index": 0}]]}, "Start Research": {"main": [[{"node": "Store Task Info", "type": "main", "index": 0}]]}, "Store Task Info": {"main": [[{"node": "Wait for Completion", "type": "main", "index": 0}]]}, "Wait for Completion": {"main": [[{"node": "Get Results", "type": "main", "index": 0}]]}, "Get Results": {"main": [[{"node": "Check Completion", "type": "main", "index": 0}]]}, "Check Completion": {"main": [[{"node": "Format Report", "type": "main", "index": 0}], [{"node": "Retry Wait", "type": "main", "index": 0}]]}, "Format Report": {"main": [[{"node": "Aggregate Reports", "type": "main", "index": 0}]]}, "Retry Wait": {"main": [[{"node": "Get Results", "type": "main", "index": 0}]]}, "Aggregate Reports": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Email": {"main": [[{"node": "Send Email Report", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"instanceId": "deep-research-api"}, "id": "scheduled-research-workflow", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "research", "name": "research"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "scheduled", "name": "scheduled"}]}