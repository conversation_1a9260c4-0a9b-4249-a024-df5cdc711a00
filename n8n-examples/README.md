# N8N Integration Examples for Deep Research API

This directory contains example N8N workflows for integrating with the Deep Research API. These workflows demonstrate common use cases and can be imported directly into your N8N instance.

## 🎉 Production Ready Workflows

All workflows in this directory are **pre-configured for the live production API** at:
- **Base URL**: `https://deep.w-post.com`
- **API Key**: `12345` (for testing - change in production)
- **SSL**: Fully configured with Let's Encrypt certificates

## Prerequisites

1. **N8N Instance**: You need a running N8N instance (cloud or self-hosted)
2. **Network Access**: Your N8N instance must be able to reach `https://deep.w-post.com`
3. **API Key**: Use `12345` for testing, or request a production API key

## Quick Start

1. **Import Workflows**: Import the JSON files from this directory into your N8N instance
2. **Configure Credentials**: Set up HTTP Header Auth credentials:
   - **Name**: Deep Research API
   - **Header Name**: `X-API-Key`
   - **Header Value**: `12345`
3. **Test Connectivity**: Run the `test-connectivity.json` workflow first
4. **Deploy**: Use the other workflows for your specific use cases

## Available Workflows

### 1. **Test Connectivity** (`test-connectivity.json`) ⭐ **START HERE**
- **Purpose**: Test API connectivity and basic functionality
- **Use Case**: Verify your n8n setup works with the API
- **Trigger**: Manual execution
- **Output**: Health check, API info, and basic research test results

### 2. **Basic Research Workflow** (`basic-research.json`)
- **Purpose**: Start a research task and wait for completion
- **Use Case**: Simple research automation
- **Trigger**: Manual or webhook
- **Output**: Research report (JSON response with markdown)

### 3. **Scheduled Research** (`scheduled-research.json`)
- **Purpose**: Automatically research topics on a schedule
- **Use Case**: Daily/weekly research reports
- **Trigger**: Cron schedule (weekdays at 9 AM)
- **Output**: Email with research results

### 4. **Webhook Research with Notifications** (`webhook-research-with-notifications.json`)
- **Purpose**: Research with progress notifications
- **Use Case**: Long-running research with status updates
- **Trigger**: Webhook endpoint
- **Output**: Slack/Discord notifications + final report

### 5. **Research with Markdown Extraction** (`research-with-markdown-extraction.json`) 📄 **NEW**
- **Purpose**: Complete research workflow with result extraction
- **Use Case**: Get full markdown reports and save to various destinations
- **Trigger**: Manual execution
- **Output**: Extracted markdown report, learnings, and sources

## 📄 **Accessing Research Results**

### **Result Structure**
Research tasks return results in this format:
```json
{
  "taskId": "uuid",
  "status": "completed",
  "result": {
    "answer": "Short answer",              // For outputType: "answer"
    "reportMarkdown": "# Full Report...", // For outputType: "report"
    "learnings": ["fact1", "fact2"],
    "visitedUrls": ["url1", "url2"]
  }
}
```

### **Extracting Markdown Reports in n8n**
To get the markdown report in your n8n workflow:

1. **For Answer Format**: Use `{{ $json.result.answer }}`
2. **For Report Format**: Use `{{ $json.result.reportMarkdown }}`
3. **For Learnings**: Use `{{ $json.result.learnings }}`
4. **For Sources**: Use `{{ $json.result.visitedUrls }}`

### **Saving Markdown Results**

#### **Option 1: Save to Google Drive**
Add a Google Drive node after extracting results:
```
Node: Google Drive
Action: Upload a file
File Name: research-report-{{ $now.format('YYYY-MM-DD-HH-mm') }}.md
File Content: {{ $json.markdown_report }}
```

#### **Option 2: Save to Dropbox**
Add a Dropbox node:
```
Node: Dropbox
Action: Upload
Path: /research-reports/{{ $json.task_id }}.md
Content: {{ $json.markdown_report }}
```

#### **Option 3: Send via Email**
Add an Email node:
```
Node: Send Email
Subject: Research Report: {{ $node.Configuration.json.research_query }}
Text: {{ $json.markdown_report }}
Attachments: Create .md file with the content
```

#### **Option 4: Save to Database**
Add a database node (MySQL, PostgreSQL, etc.):
```
Node: MySQL
Query: INSERT INTO research_reports (task_id, query, markdown_content, created_at)
       VALUES ('{{ $json.task_id }}', '{{ $node.Configuration.json.research_query }}',
               '{{ $json.markdown_report }}', NOW())
```

#### **Option 5: Post to Notion**
Add a Notion node:
```
Node: Notion
Action: Create a page
Title: Research: {{ $node.Configuration.json.research_query }}
Content: Convert markdown to Notion blocks
```

## Configuration

### ✅ Pre-Configured Settings
All workflows are already configured with:
- **API Base URL**: `https://deep.w-post.com`
- **Authentication**: Header Auth with `X-API-Key: 12345`
- **SSL/TLS**: Enabled with automatic certificate verification
- **Headers**: Properly set for JSON communication

### Setting Up Credentials in N8N
1. Go to **Settings** → **Credentials** in your N8N instance
2. Click **Create New Credential**
3. Select **HTTP Header Auth**
4. Configure:
   - **Name**: `Deep Research API`
   - **Header Name**: `X-API-Key`
   - **Header Value**: `12345`
5. **Save** the credential

### Using Different API Keys
To use a different API key:
1. Update your credential in N8N
2. Or modify the workflows to use a different credential
3. For production, replace `12345` with your actual API key

## API Endpoints Reference

### Production API Base URL
```
https://deep.w-post.com
```

### Start Research Task
```http
POST https://deep.w-post.com/api/research
X-API-Key: 12345
Content-Type: application/json

{
  "query": "Your research topic",
  "depth": 3,
  "breadth": 4,
  "outputType": "report",
  "priority": "normal"
}
```

### Check Task Status
```http
GET https://deep.w-post.com/api/research/{taskId}
X-API-Key: 12345
```

### Get Task Progress
```http
GET https://deep.w-post.com/api/research/{taskId}/progress
X-API-Key: 12345
```

### Health Check
```http
GET https://deep.w-post.com/api/health
X-API-Key: 12345
```

### API Documentation
```http
GET https://deep.w-post.com/docs
X-API-Key: 12345
```

## Error Handling

All workflows include error handling for common scenarios:

1. **API Unavailable**: Retry logic with exponential backoff
2. **Task Failed**: Error notifications and cleanup
3. **Timeout**: Automatic task cancellation after timeout
4. **Rate Limiting**: Respect rate limits and retry after delay

## Best Practices

### 1. Polling Strategy
When waiting for task completion, use these intervals:
- **Initial delay**: 10 seconds
- **Polling interval**: 30 seconds
- **Maximum wait time**: 30 minutes
- **Exponential backoff**: Increase interval on repeated checks

### 2. Error Handling
- Always check HTTP status codes
- Handle rate limiting (429) with retry logic
- Log errors for debugging
- Implement fallback mechanisms

### 3. Resource Management
- Cancel tasks that are no longer needed
- Monitor concurrent task limits
- Clean up old completed tasks

### 4. Security
- Store API keys securely in N8N credentials
- Use HTTPS in production
- Validate input data before sending to API

## Troubleshooting

### Common Issues

1. **EHOSTUNREACH Error**
   - **Problem**: n8n shows "connect EHOSTUNREACH" error
   - **Cause**: n8n is trying to connect to IP address instead of domain name
   - **Solution**: Always use `https://deep.w-post.com` in your HTTP Request nodes
   - **Fix**: Check that your URL field contains the domain name, not an IP address

2. **SSL Certificate Errors**
   - **Problem**: SSL/TLS handshake failures
   - **Cause**: Certificate is issued for domain name, not IP address
   - **Solution**: Use the domain name `https://deep.w-post.com` in all requests
   - **Note**: Never use IP addresses with HTTPS endpoints

3. **Authentication Failed (401)**
   - **Problem**: API returns 401 Unauthorized
   - **Solution**: Verify API key is set to `12345` in your HTTP Header Auth credential
   - **Check**: Ensure the header name is exactly `X-API-Key` (case-sensitive)

4. **Connection Timeout**
   - **Problem**: Requests timeout before completing
   - **Solution**: Increase timeout values in HTTP Request node options
   - **Recommended**: Set timeout to 30 seconds for research endpoints

5. **Rate Limiting (429)**
   - **Problem**: Too many requests error
   - **Solution**: Add delays between requests
   - **Limit**: 100 requests per 15 minutes per client

### Testing Your Setup

1. **Start with Test Connectivity Workflow**
   - Import `test-connectivity.json` first
   - Set up your HTTP Header Auth credential
   - Run the workflow to verify everything works

2. **Check Execution Results**
   - Look for green checkmarks on all nodes
   - Verify the final output shows "All connectivity tests passed"
   - If any node fails, check the error message

3. **Debug Mode**
   - Enable debug mode in N8N to see detailed request/response data
   - Go to workflow settings → Enable "Save execution data"
   - Check execution logs for details

### Credential Setup Verification

To verify your credentials are set up correctly:
```bash
# Test the API directly with curl
curl -H "X-API-Key: 12345" https://deep.w-post.com/api/health

# Expected response:
{"status":"healthy","timestamp":"...","version":"0.0.1",...}
```

## Support

For issues with:
- **N8N Workflows**: Check N8N documentation and community
- **Deep Research API**: Check API logs and health endpoint
- **Integration**: Review this documentation and examples

## Production Deployment Information

### Live API Details
- **URL**: https://deep.w-post.com
- **Status**: ✅ Live and operational
- **SSL**: Let's Encrypt certificate (auto-renewal enabled)
- **Uptime**: 24/7 availability
- **Rate Limits**: 100 requests per 15 minutes
- **Authentication**: API Key required (`X-API-Key` header)

### API Capabilities
- **Research Tasks**: Asynchronous research with progress tracking
- **Multiple Output Types**: Answers, reports, and detailed analysis
- **External Integrations**: OpenAI and Firecrawl APIs
- **Real-time Progress**: WebSocket-like progress updates
- **Task Management**: Start, monitor, and cancel research tasks

### Support & Monitoring
- **Health Endpoint**: https://deep.w-post.com/api/health
- **Documentation**: https://deep.w-post.com/docs
- **OpenAPI Spec**: https://deep.w-post.com/openapi.json

## Contributing

To contribute new workflow examples:
1. Create the workflow in N8N
2. Export as JSON
3. Use `https://deep.w-post.com` as the base URL
4. Test thoroughly with the live API
5. Add documentation and submit with clear description of use case
