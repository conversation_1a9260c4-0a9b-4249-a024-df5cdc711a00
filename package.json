{"name": "open-deep-research", "version": "0.0.1", "main": "index.ts", "scripts": {"format": "prettier --write \"src/**/*.{ts,tsx}\"", "tsx": "tsx --env-file=.env.local", "start": "tsx --env-file=.env.local src/run.ts", "api": "tsx --env-file=.env.local src/api.ts", "docker": "tsx src/run.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.0", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.8", "prettier": "^3.4.2", "tsx": "^4.19.2", "typescript": "^5.7.3"}, "dependencies": {"@ai-sdk/fireworks": "^0.1.14", "@ai-sdk/openai": "^1.1.9", "@mendable/firecrawl-js": "^1.16.0", "ai": "^4.1.17", "cors": "^2.8.5", "express": "^4.18.3", "js-tiktoken": "^1.0.17", "lodash-es": "^4.17.21", "p-limit": "^6.2.0", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "zod": "^3.24.1"}, "engines": {"node": "22.x"}}