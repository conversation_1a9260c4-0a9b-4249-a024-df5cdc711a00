# Deep Research API - Production Deployment Guide

## 🎉 PRODUCTION DEPLOYMENT COMPLETED

**Status**: ✅ **LIVE AND OPERATIONAL**
**URL**: https://deep.w-post.com
**SSL**: ✅ Let's Encrypt certificate auto-generated
**Authentication**: ✅ API Key: `12345`
**Deployment Date**: June 25, 2025

---

This guide covers deploying the Deep Research API for production use with external access capabilities.

## Quick Start

1. **Clone and Configure**
   ```bash
   git clone <repository-url>
   cd deep-research
   cp .env.example .env.local
   # Edit .env.local with your API keys
   ```

2. **Deploy with Docker**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh prod
   ```

3. **Access the API**
   - API: `http://your-server-ip:3051`
   - Documentation: `http://your-server-ip:3051/docs`
   - Health Check: `http://your-server-ip:3051/api/health`

## Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended)
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Storage**: 10GB available space
- **Network**: Open ports 80, 443, 3051

### Software Dependencies
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Git**: For cloning the repository

### API Keys Required
- **Firecrawl API Key**: For web search and content extraction
- **OpenAI API Key**: For AI model access (or Fireworks API key for R1 model)

## Environment Configuration

### Core Settings (.env.local)
```bash
# API Configuration
PORT=3051
NODE_ENV=production

# Authentication (recommended for production)
REQUIRE_AUTH=true
API_KEY=your_secure_api_key_here

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# CORS (adjust for your domain)
CORS_ORIGIN=https://yourdomain.com

# Research API Keys
FIRECRAWL_KEY=your_firecrawl_key
OPENAI_KEY=your_openai_key

# Security
ENABLE_SECURITY_HEADERS=true
TRUST_PROXY=true

# Performance
MAX_CONCURRENT_TASKS=10
FIRECRAWL_CONCURRENCY=3
```

### Optional Settings
```bash
# Alternative AI Models
FIREWORKS_KEY=your_fireworks_key
CUSTOM_MODEL=custom_model_name
OPENAI_ENDPOINT=https://api.openai.com/v1

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Task Management
TASK_TIMEOUT_DEFAULT=3600
TASK_CLEANUP_INTERVAL=3600000
```

## Deployment Options

### Option 1: Simple Docker Deployment
Best for development and small-scale production.

```bash
# Start the API service only
./deploy.sh dev

# Check status
./deploy.sh status

# Stop services
./deploy.sh stop
```

### Option 2: Production with Nginx
Recommended for production with SSL and load balancing.

```bash
# Start API + Nginx reverse proxy
./deploy.sh prod

# Check all services
docker-compose --profile production ps
```

### Option 3: Manual Docker Commands
For custom deployments.

```bash
# Build the image
docker build -t deep-research-api .

# Run the container
docker run -d \
  --name deep-research-api \
  -p 3051:3051 \
  --env-file .env.local \
  --restart unless-stopped \
  deep-research-api

# Check logs
docker logs deep-research-api
```

## SSL/HTTPS Configuration

### 1. Obtain SSL Certificates
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com

# Or use your existing certificates
mkdir ssl
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem
```

### 2. Update Nginx Configuration
Edit `nginx.conf` and uncomment the HTTPS server block:
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    # ... rest of SSL configuration
}
```

### 3. Restart Services
```bash
docker-compose --profile production restart nginx
```

## External Access Configuration

### Firewall Setup
```bash
# Ubuntu/Debian with UFW
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 3051  # API (if not using nginx)
sudo ufw enable

# CentOS/RHEL with firewalld
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3051/tcp
sudo firewall-cmd --reload
```

### Network Configuration
1. **Cloud Providers**: Configure security groups to allow inbound traffic
2. **Home/Office**: Configure router port forwarding
3. **VPS**: Most providers allow all outbound traffic by default

### DNS Setup
Point your domain to the server IP:
```
A    yourdomain.com    -> your.server.ip.address
A    api.yourdomain.com -> your.server.ip.address
```

## Monitoring and Maintenance

### Health Checks
```bash
# API health
curl http://localhost:3051/api/health

# Service status
docker-compose ps

# Resource usage
docker stats
```

### Log Management
```bash
# View API logs
docker-compose logs deep-research-api

# Follow logs in real-time
docker-compose logs -f deep-research-api

# Nginx logs (if using)
docker-compose logs nginx
```

### Backup Strategy
```bash
# Backup configuration
tar -czf backup-$(date +%Y%m%d).tar.gz .env.local nginx.conf ssl/

# Backup logs (optional)
docker-compose logs > logs-backup-$(date +%Y%m%d).log
```

### Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose --profile production up --build -d

# Or use the deploy script
./deploy.sh prod
```

## Performance Optimization

### Resource Limits
Add to docker-compose.yml:
```yaml
services:
  deep-research-api:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

### Scaling
For high-traffic scenarios:
1. **Horizontal Scaling**: Run multiple API instances behind a load balancer
2. **Database**: Replace in-memory storage with Redis/PostgreSQL
3. **Queue System**: Add Redis for task queue management
4. **CDN**: Use CloudFlare or similar for static content

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   sudo lsof -i :3051
   sudo kill -9 <PID>
   ```

2. **Permission Denied**
   ```bash
   sudo chown -R $USER:$USER .
   chmod +x deploy.sh
   ```

3. **API Keys Not Working**
   - Verify keys in .env.local
   - Check API key quotas/limits
   - Ensure proper format (no extra spaces)

4. **Memory Issues**
   ```bash
   # Check memory usage
   free -h
   docker stats
   
   # Restart services
   docker-compose restart
   ```

### Debug Mode
Enable detailed logging:
```bash
# Set in .env.local
LOG_LEVEL=debug

# Restart services
docker-compose restart
```

## Security Considerations

### Production Checklist
- [ ] Enable authentication (`REQUIRE_AUTH=true`)
- [ ] Use strong API keys (32+ characters)
- [ ] Enable security headers
- [ ] Configure proper CORS origins
- [ ] Use HTTPS in production
- [ ] Regular security updates
- [ ] Monitor access logs
- [ ] Implement rate limiting
- [ ] Use non-root containers
- [ ] Regular backups

### API Key Management
```bash
# Generate secure API key
openssl rand -hex 32

# Rotate keys regularly
# Update .env.local and restart services
```

## Support and Maintenance

### Regular Tasks
- **Weekly**: Check logs and resource usage
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and rotate API keys
- **Annually**: Review and update SSL certificates

### Getting Help
1. Check the API health endpoint
2. Review application logs
3. Consult the API documentation at `/docs`
4. Check GitHub issues and documentation

---

## ✅ PRODUCTION DEPLOYMENT VERIFICATION

### Infrastructure Status (June 25, 2025)
- **✅ Docker Containers**: Running successfully
- **✅ Caddy Reverse Proxy**: Operational with automatic SSL
- **✅ API Service**: Responding on port 3051
- **✅ HTTPS**: Let's Encrypt certificate active
- **✅ DNS**: Pointing to server IP *************
- **✅ Authentication**: API key validation working

### Verified Endpoints
```bash
# ✅ Health Check - WORKING
curl -H "X-API-Key: 12345" https://deep.w-post.com/api/health

# ✅ API Info - WORKING
curl -H "X-API-Key: 12345" https://deep.w-post.com/api/info

# ✅ Documentation - WORKING
curl -H "X-API-Key: 12345" https://deep.w-post.com/docs

# ✅ OpenAPI Spec - WORKING
curl -H "X-API-Key: 12345" https://deep.w-post.com/openapi.json

# ✅ Research Task Creation - WORKING
curl -X POST -H "X-API-Key: 12345" \
  -H "Content-Type: application/json" \
  -d '{"query":"test","depth":1,"breadth":1}' \
  https://deep.w-post.com/api/research
```

### n8n Webhook Integration
The API is fully compatible with n8n webhooks:
```javascript
// n8n HTTP Request Node Configuration
{
  "method": "POST",
  "url": "https://deep.w-post.com/api/research",
  "headers": {
    "X-API-Key": "12345",
    "Content-Type": "application/json"
  },
  "body": {
    "query": "{{$json.query}}",
    "depth": 3,
    "breadth": 3,
    "outputType": "report"
  }
}
```

### SSL Certificate Details
- **Issuer**: Let's Encrypt (E5)
- **Subject**: CN=deep.w-post.com
- **Valid From**: Jun 25 15:26:58 2025 GMT
- **Valid Until**: Sep 23 15:26:57 2025 GMT
- **Protocol**: TLS 1.3 with HTTP/2

### Current Configuration
- **Domain**: deep.w-post.com
- **Server IP**: *************
- **API Key**: 12345 (for testing)
- **Reverse Proxy**: Caddy with automatic SSL
- **Container Network**: deep-research-network
