#!/bin/bash

# Deep Research API Test Script
# This script tests the basic functionality of the Deep Research API

set -e

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:3051}"
API_KEY="${API_KEY:-}"
TIMEOUT=300  # 5 minutes timeout for research completion

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to make API requests
api_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local headers=""
    
    if [ -n "$API_KEY" ]; then
        headers="-H 'X-API-Key: $API_KEY'"
    fi
    
    if [ -n "$data" ]; then
        eval curl -s -X "$method" "$API_BASE_URL$endpoint" \
            -H "Content-Type: application/json" \
            $headers \
            -d "'$data'"
    else
        eval curl -s -X "$method" "$API_BASE_URL$endpoint" \
            $headers
    fi
}

# Function to check if jq is available
check_jq() {
    if ! command -v jq &> /dev/null; then
        log_warning "jq is not installed. JSON responses will not be formatted."
        return 1
    fi
    return 0
}

# Function to extract JSON field
extract_json() {
    local json="$1"
    local field="$2"
    
    if check_jq; then
        echo "$json" | jq -r ".$field"
    else
        # Fallback without jq (basic extraction)
        echo "$json" | grep -o "\"$field\":\"[^\"]*\"" | cut -d'"' -f4
    fi
}

# Test functions
test_health_check() {
    log_info "Testing health check endpoint..."
    
    local response
    response=$(api_request "GET" "/api/health")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        local status
        status=$(extract_json "$response" "status")
        
        if [ "$status" = "healthy" ]; then
            log_success "Health check passed"
            return 0
        else
            log_error "Health check failed: $response"
            return 1
        fi
    else
        log_error "Failed to connect to API at $API_BASE_URL"
        return 1
    fi
}

test_api_info() {
    log_info "Testing API info endpoint..."
    
    local response
    response=$(api_request "GET" "/api/info")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        log_success "API info retrieved successfully"
        if check_jq; then
            echo "$response" | jq .
        else
            echo "$response"
        fi
        return 0
    else
        log_error "Failed to get API info"
        return 1
    fi
}

test_start_research() {
    log_info "Testing research task creation..."
    
    local query="What are the key benefits of renewable energy?"
    local data="{\"query\":\"$query\",\"depth\":2,\"breadth\":2,\"outputType\":\"report\"}"
    
    local response
    response=$(api_request "POST" "/api/research" "$data")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        local task_id
        task_id=$(extract_json "$response" "taskId")
        
        if [ -n "$task_id" ] && [ "$task_id" != "null" ]; then
            log_success "Research task created: $task_id"
            echo "$task_id"
            return 0
        else
            log_error "Failed to extract task ID from response: $response"
            return 1
        fi
    else
        log_error "Failed to create research task"
        return 1
    fi
}

test_task_status() {
    local task_id="$1"
    
    log_info "Testing task status retrieval for task: $task_id"
    
    local response
    response=$(api_request "GET" "/api/research/$task_id")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        local status
        status=$(extract_json "$response" "status")
        
        log_success "Task status: $status"
        echo "$status"
        return 0
    else
        log_error "Failed to get task status"
        return 1
    fi
}

test_task_progress() {
    local task_id="$1"
    
    log_info "Testing task progress retrieval for task: $task_id"
    
    local response
    response=$(api_request "GET" "/api/research/$task_id/progress")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        log_success "Task progress retrieved successfully"
        if check_jq; then
            echo "$response" | jq .
        fi
        return 0
    else
        log_error "Failed to get task progress"
        return 1
    fi
}

test_list_tasks() {
    log_info "Testing task list retrieval..."
    
    local response
    response=$(api_request "GET" "/api/research?limit=5")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        log_success "Task list retrieved successfully"
        if check_jq; then
            local total
            total=$(extract_json "$response" "pagination.total")
            log_info "Total tasks: $total"
        fi
        return 0
    else
        log_error "Failed to get task list"
        return 1
    fi
}

wait_for_completion() {
    local task_id="$1"
    local start_time
    start_time=$(date +%s)
    
    log_info "Waiting for task completion (timeout: ${TIMEOUT}s)..."
    
    while true; do
        local current_time
        current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [ $elapsed -gt $TIMEOUT ]; then
            log_error "Timeout waiting for task completion"
            return 1
        fi
        
        local status
        status=$(test_task_status "$task_id")
        
        case "$status" in
            "completed")
                log_success "Task completed successfully!"
                return 0
                ;;
            "failed")
                log_error "Task failed"
                return 1
                ;;
            "cancelled")
                log_error "Task was cancelled"
                return 1
                ;;
            "running"|"pending")
                log_info "Task status: $status (elapsed: ${elapsed}s)"
                sleep 10
                ;;
            *)
                log_warning "Unknown status: $status"
                sleep 10
                ;;
        esac
    done
}

test_cancel_task() {
    local task_id="$1"
    
    log_info "Testing task cancellation for task: $task_id"
    
    local response
    response=$(api_request "POST" "/api/research/$task_id/cancel")
    local status_code=$?
    
    if [ $status_code -eq 0 ]; then
        local status
        status=$(extract_json "$response" "status")
        
        if [ "$status" = "cancelled" ]; then
            log_success "Task cancelled successfully"
            return 0
        else
            log_warning "Task cancellation response: $response"
            return 1
        fi
    else
        log_error "Failed to cancel task"
        return 1
    fi
}

# Main test execution
main() {
    echo "🧪 Deep Research API Test Suite"
    echo "================================"
    echo "API Base URL: $API_BASE_URL"
    echo "Authentication: $([ -n "$API_KEY" ] && echo "Enabled" || echo "Disabled")"
    echo ""
    
    local failed_tests=0
    local total_tests=0
    
    # Test 1: Health Check
    ((total_tests++))
    if ! test_health_check; then
        ((failed_tests++))
    fi
    echo ""
    
    # Test 2: API Info
    ((total_tests++))
    if ! test_api_info; then
        ((failed_tests++))
    fi
    echo ""
    
    # Test 3: Start Research Task
    ((total_tests++))
    local task_id
    task_id=$(test_start_research)
    if [ $? -ne 0 ]; then
        ((failed_tests++))
        log_error "Cannot continue with remaining tests"
        exit 1
    fi
    echo ""
    
    # Test 4: Task Status
    ((total_tests++))
    if ! test_task_status "$task_id" > /dev/null; then
        ((failed_tests++))
    fi
    echo ""
    
    # Test 5: Task Progress
    ((total_tests++))
    if ! test_task_progress "$task_id"; then
        ((failed_tests++))
    fi
    echo ""
    
    # Test 6: List Tasks
    ((total_tests++))
    if ! test_list_tasks; then
        ((failed_tests++))
    fi
    echo ""
    
    # Test 7: Wait for Completion (optional)
    if [ "${WAIT_FOR_COMPLETION:-false}" = "true" ]; then
        ((total_tests++))
        if ! wait_for_completion "$task_id"; then
            ((failed_tests++))
        fi
        echo ""
    else
        log_info "Skipping completion wait (set WAIT_FOR_COMPLETION=true to enable)"
        
        # Test cancellation instead
        ((total_tests++))
        if ! test_cancel_task "$task_id"; then
            ((failed_tests++))
        fi
        echo ""
    fi
    
    # Summary
    echo "📊 Test Results"
    echo "==============="
    echo "Total tests: $total_tests"
    echo "Passed: $((total_tests - failed_tests))"
    echo "Failed: $failed_tests"
    
    if [ $failed_tests -eq 0 ]; then
        log_success "All tests passed! 🎉"
        exit 0
    else
        log_error "$failed_tests test(s) failed"
        exit 1
    fi
}

# Show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Test the Deep Research API functionality"
    echo ""
    echo "Environment Variables:"
    echo "  API_BASE_URL          API base URL (default: http://localhost:3051)"
    echo "  API_KEY              API key for authentication (optional)"
    echo "  WAIT_FOR_COMPLETION  Wait for research completion (default: false)"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Basic test"
    echo "  API_KEY=your_key $0                  # Test with authentication"
    echo "  WAIT_FOR_COMPLETION=true $0          # Wait for task completion"
    echo "  API_BASE_URL=http://remote:3051 $0   # Test remote API"
}

# Handle command line arguments
case "${1:-}" in
    "-h"|"--help"|"help")
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
